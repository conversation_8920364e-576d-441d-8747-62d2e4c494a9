# Perspective Racing

A comprehensive sailing performance and race management system that runs in the browser with responsive design for tablet/laptop devices. The application connects to an NKE WiFi Box streaming NMEA 0183 sentences, computes real-time VMG (Velocity Made Good) performance against optimal polar models, visualizes live sailing metrics, and provides integrated race planning, post-race analytics, and crew scheduling functionality.

## Features

### Real-time Performance Tracking
- Live sailing metrics display (boat speed, wind speed/angle, VMG)
- Sea state indicator with automatic/manual toggle
- Sail plan selector with dropdowns for headsail, spinnaker, and reef configurations
- Performance percentage badge showing efficiency against polar targets
- Color-coded performance indicators and real-time recommendations

### Polar Performance Engine
- In-memory polar table storage and interpolation
- Real-time target VMG calculation based on current conditions
- Performance percentage computation against optimal polar curves
- Adaptive polar table updates and sea state compensation

### Race Planning & Management
- Interactive map with race course mark placement
- Start timer with countdown functionality
- Weather overlay integration via Windy.com
- Course strategy planning tools
- Waypoint and route management

### Post-Race Analytics
- Performance summary statistics
- Time-series charts comparing actual vs target performance
- Tack and jibe analysis with efficiency metrics
- Data export functionality
- Historical performance tracking

### Crew Management
- Race calendar with event scheduling
- Crew availability tracking and RSVP system
- Crew member database management
- Automated scheduling notifications

## Technical Stack

### Frontend
- **React 18+** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **shadcn/ui** component library
- **Recharts** for data visualization
- **Leaflet.js** for interactive mapping
- **Socket.IO Client** for real-time communication

### Backend
- **Node.js** with Express.js
- **TypeScript** for type safety
- **Socket.IO** for WebSocket communication
- **Prisma ORM** with SQLite database
- **Zod** for data validation

### Real-time Data Processing
- **NMEA 0183** sentence parsing (VHW, MWV, GLL, XDR formats)
- **TCP socket listener** with configurable port
- **Performance calculator** with smoothing algorithms
- **Polar engine** with interpolation and sea state compensation

## Prerequisites

- **Node.js** 18+
- **npm** or **yarn**
- **NKE WiFi Box** or NMEA data source (optional - includes simulator)

## Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd perspective-racing
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your configuration
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Create and migrate database
npm run db:migrate

# (Optional) Open Prisma Studio
npm run db:studio
```

### 4. Development Mode

```bash
# Start both frontend and backend
npm run dev

# Or start individually:
npm run dev:client  # Frontend only (port 5173)
npm run dev:server  # Backend only (port 8080)
```

### 5. NMEA Data Source

**Option A: Real NKE WiFi Box**
- Configure your NKE WiFi Box to output NMEA data on TCP
- Update `.env` with correct `NMEA_HOST` and `NMEA_PORT`

**Option B: NMEA Simulator (for testing)**
```bash
# In a separate terminal
cd tools/nmea-simulator
node simulator.js
```

The application will be available at:
- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:8080
- **Health Check**: http://localhost:8080/health

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | `file:./dev.db` | SQLite database path |
| `PORT` | `8080` | Backend server port |
| `NODE_ENV` | `development` | Environment mode |
| `CLIENT_URL` | `http://localhost:5173` | Frontend URL for CORS |
| `NMEA_HOST` | `localhost` | NMEA data source host |
| `NMEA_PORT` | `2000` | NMEA data source port |

### Frontend Environment Variables

Create a `.env.local` file in the root directory:

```bash
# Use mock data instead of real NMEA connection
VITE_USE_MOCK_DATA=false

# Backend server URL
VITE_SERVER_URL=http://localhost:8080
```

## Production Deployment

### Docker Deployment

```bash
# Build and run with Docker Compose
docker-compose up -d

# Or build manually
docker build -t perspective-racing .
docker run -p 3000:3000 -p 8080:8080 perspective-racing
```

### Cloud Deployment

**Vercel (Frontend) + Render (Backend)**

1. **Frontend on Vercel:**
   ```bash
   # Build command
   npm run build:client

   # Output directory
   dist/client
   ```

2. **Backend on Render:**
   ```bash
   # Build command
   npm run build:server

   # Start command
   npm start
   ```

### Raspberry Pi Deployment

Perfect for on-boat installation:

```bash
# Clone repository on Pi
git clone <repository-url>
cd perspective-racing

# Install dependencies
npm install

# Setup production environment
cp .env.example .env
# Edit .env for production settings

# Build application
npm run build

# Start with PM2 for process management
npm install -g pm2
pm2 start npm --name "perspective-racing" -- start
pm2 save
pm2 startup
```

## API Documentation

### REST Endpoints

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/api/polars` | GET | Get all polar tables |
| `/api/polars` | POST | Create new polar table |
| `/api/polars/:id/activate` | POST | Set active polar table |
| `/api/races` | GET | Get all race events |
| `/api/races` | POST | Create new race event |
| `/api/crew` | GET | Get all crew members |
| `/api/crew` | POST | Create new crew member |
| `/api/rsvps` | POST | Create/update RSVP |
| `/api/performance` | GET | Get performance data |
| `/api/sailplan` | GET | Get current sail plan |
| `/api/sailplan` | PUT | Update sail plan |
| `/api/settings` | GET | Get all settings |

### WebSocket Events

**Client → Server:**
- `subscribe:liveData` - Subscribe to live performance data
- `subscribe:race` - Subscribe to race updates
- `sailPlan:update` - Update current sail plan
- `race:start` - Start a race
- `race:finish` - Finish a race

**Server → Client:**
- `liveUpdate` - Live performance data
- `sailPlan:updated` - Sail plan changed
- `race:started` - Race started
- `race:finished` - Race finished
- `status:broadcast` - System status update

## NMEA Data Format

The application supports these NMEA 0183 sentences:

| Sentence | Description | Example |
|----------|-------------|---------|
| `VHW` | Water speed and heading | `$IIVHW,45.0,T,,M,7.5,N,,K*hh` |
| `MWV` | Wind speed and angle | `$IIMWV,45.0,T,12.0,N,A*hh` |
| `GLL` | Geographic position | `$IIGLL,3405.22,N,11814.37,W,123456,A,A*hh` |
| `XDR` | Transducer measurement | `$IIXDR,A,8.5,D,HEEL*hh` |
| `HDT` | True heading | `$IIHDT,45.0,T*hh` |
| `VTG` | Track and ground speed | `$IIVTG,45.0,T,,M,7.2,N,,K,A*hh` |
| `DBT` | Depth below transducer | `$IIDBT,49.2,f,15.0,M,8.2,F*hh` |

## Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run backend tests only
cd server && npm test

# Start NMEA simulator for testing
cd tools/nmea-simulator && node simulator.js
```

## Troubleshooting

### Common Issues

**1. NMEA Connection Failed**
- Check `NMEA_HOST` and `NMEA_PORT` in `.env`
- Verify NKE WiFi Box is broadcasting on correct port
- Use NMEA simulator for testing: `node tools/nmea-simulator/simulator.js`

**2. Database Issues**
```bash
# Reset database
rm dev.db
npm run db:migrate
```

**3. Build Errors**
```bash
# Clear node modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

**4. Port Conflicts**
- Frontend (5173): Change in `vite.config.ts`
- Backend (8080): Change `PORT` in `.env`
- NMEA (2000): Change `NMEA_PORT` in `.env`

### Performance Optimization

**For Raspberry Pi:**
- Use `NODE_ENV=production`
- Enable gzip compression
- Limit database retention days
- Use PM2 for process management

**For Marine Environment:**
- Enable offline mode in PWA settings
- Increase data smoothing window for rough conditions
- Use conservative polar tables for safety

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Commit changes: `git commit -am 'Add new feature'`
4. Push to branch: `git push origin feature/new-feature`
5. Submit a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the API documentation

---

**⛵ Happy Sailing! ⛵**
