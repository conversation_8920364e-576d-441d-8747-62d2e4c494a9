import React, { useState, useEffect, useContext } from 'react';
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>
} from 'recharts';
import { PerformanceDataPoint } from '../../types';
import { fetchPerformanceDataForRace } from '../../services/api';
import { PolarContext } from '../../contexts/PolarContext';

interface TimeChartProps {
  raceId: string;
}

type ChartMetric = 'bsp' | 'tws' | 'vmg' | 'percentPolar' | 'heel';

const LoadingState: React.FC<{ message: string }> = ({ message }) => (
  <div className="flex flex-col items-center justify-center h-full perspective-racing-text-secondary">
    <svg className="animate-spin h-8 w-8 perspective-racing-accent-sky mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    {message}
  </div>
);

const metricOptions: { value: ChartMetric; label: string, unit: string, targetKey?: string }[] = [
  { value: 'vmg', label: 'VMG', unit: 'kts', targetKey: 'targetVMG' },
  { value: 'bsp', label: 'Boat Speed', unit: 'kts', targetKey: 'targetBoatSpeed' },
  { value: 'percentPolar', label: '% Polar', unit: '%', targetKey: 'targetForMetric' },
  { value: 'tws', label: 'True Wind Speed', unit: 'kts' },
  { value: 'heel', label: 'Heel Angle', unit: '°' },
];

const TimeChart: React.FC<TimeChartProps> = ({ raceId }) => {
  const [data, setData] = useState<PerformanceDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedMetric, setSelectedMetric] = useState<ChartMetric>('vmg');
  const { getPolarTarget } = useContext(PolarContext);


  useEffect(() => {
    const loadData = async () => {
      if (!raceId) {
        setData([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      setError(null);
      try {
        const performanceData = await fetchPerformanceDataForRace(raceId);
        const augmentedData = performanceData.map(dp => {
          const polarTarget = getPolarTarget(dp.tws, dp.twa);
          const targetBoatSpeed = polarTarget ? polarTarget.targetSpeed : dp.bsp * 0.9;
          const targetVMGVal = targetBoatSpeed * Math.cos(dp.twa * Math.PI / 180);

          let targetForMetric;
          switch(selectedMetric) {
            case 'bsp': targetForMetric = targetBoatSpeed; break;
            case 'vmg': targetForMetric = targetVMGVal; break;
            case 'percentPolar': targetForMetric = 100; break;
            default: targetForMetric = undefined;
          }

          return {
            ...dp,
            time: new Date(dp.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
            targetBoatSpeed: parseFloat(targetBoatSpeed.toFixed(1)),
            targetVMG: parseFloat(targetVMGVal.toFixed(1)),
            targetForMetric: targetForMetric !== undefined ? parseFloat(targetForMetric.toFixed(1)) : undefined,
          };
        });
        setData(augmentedData);
      } catch (err) {
        console.error("Failed to load time chart data:", err);
        setError("Could not load performance data for this race.");
      } finally {
        setLoading(false);
      }
    };
    loadData();
  }, [raceId, getPolarTarget, selectedMetric]);



  const chartContent = () => {
    if (loading) return <LoadingState message="Loading chart data..." />;
    if (error) return (
      <div className="h-full flex items-center justify-center text-destructive">
        <div className="flex items-center gap-2">
          <div className="h-5 w-5 rounded-full bg-destructive/20 flex items-center justify-center">
            <div className="h-2 w-2 rounded-full bg-destructive"></div>
          </div>
          {error}
        </div>
      </div>
    );

    if (data.length === 0) return (
      <div className="h-full flex items-center justify-center text-muted-foreground">
        <div className="text-center">
          <div className="h-12 w-12 mx-auto mb-3 rounded-full bg-muted flex items-center justify-center">
            <svg className="h-6 w-6 text-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <p>No performance data available for this race</p>
        </div>
      </div>
    );

    const currentMetricConfig = metricOptions.find(m => m.value === selectedMetric);

    return (
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 5, right: 25, left: 20, bottom: 20 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
          <XAxis
            dataKey="time"
            stroke="hsl(var(--muted-foreground))"
            tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))' }}
            axisLine={{ stroke: 'hsl(var(--border))' }}
            tickLine={{ stroke: 'hsl(var(--border))' }}
          />
          <YAxis
            stroke="hsl(var(--muted-foreground))"
            tick={{ fontSize: 11, fill: 'hsl(var(--muted-foreground))' }}
            axisLine={{ stroke: 'hsl(var(--border))' }}
            tickLine={{ stroke: 'hsl(var(--border))' }}
            label={{
              value: currentMetricConfig?.unit,
              angle: -90,
              position: 'insideLeft',
              fill: 'hsl(var(--muted-foreground))',
              style: { textAnchor: 'middle' },
              fontSize: 12
            }}
            domain={selectedMetric === 'percentPolar' ? [70, 110] : ['auto', 'auto']}
          />
          <Tooltip
            contentStyle={{
              backgroundColor: 'hsl(var(--popover))',
              border: '1px solid hsl(var(--border))',
              borderRadius: '8px',
              boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
              fontSize: '12px'
            }}
            labelStyle={{
              color: 'hsl(var(--popover-foreground))',
              fontWeight: '600',
              marginBottom: '8px',
              borderBottom: '1px solid hsl(var(--border))',
              paddingBottom: '4px'
            }}
            itemStyle={{
              color: 'hsl(var(--popover-foreground))',
              padding: '2px 0',
              fontSize: '11px'
            }}
            cursor={{ stroke: 'hsl(var(--primary))', strokeWidth: 1, strokeDasharray: '3 3' }}
          />
          <Legend
            wrapperStyle={{ paddingTop: '15px' }}
            iconSize={12}
            formatter={(value) => (
              <span style={{
                color: 'hsl(var(--foreground))',
                fontSize: '12px',
                marginLeft: '6px',
                fontWeight: '500'
              }}>
                {value}
              </span>
            )}
          />
          <Line
            type="monotone"
            dataKey={selectedMetric}
            name={currentMetricConfig?.label || 'Actual'}
            stroke="hsl(var(--primary))"
            strokeWidth={2.5}
            dot={false}
            activeDot={{ r: 5, strokeWidth: 2, fill: 'hsl(var(--primary))', stroke: 'hsl(var(--background))' }}
          />
          {currentMetricConfig?.targetKey && data.some(d => d[currentMetricConfig.targetKey as keyof PerformanceDataPoint] !== undefined) && (
            <Line
              type="monotone"
              dataKey={currentMetricConfig.targetKey}
              name={`Target ${currentMetricConfig?.label || ''}`}
              stroke="hsl(var(--muted-foreground))"
              strokeWidth={2}
              strokeDasharray="6 4"
              dot={false}
              activeDot={{ r: 4, strokeWidth: 2, fill: 'hsl(var(--muted-foreground))', stroke: 'hsl(var(--background))' }}
            />
          )}
        </LineChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="h-[450px] perspective-racing-input-bg p-4 rounded-lg shadow-md flex flex-col"> {/* Changed background */}
      <div className="mb-4 flex justify-end">
        <select
          value={selectedMetric}
          onChange={(e) => setSelectedMetric(e.target.value as ChartMetric)}
          className="appearance-none block perspective-racing-input-bg border perspective-racing-border rounded-md px-3 py-1.5 text-sm focus:ring-sky-500 focus:border-sky-500 h-9 perspective-racing-text-primary"
        >
          {metricOptions.map(opt => (
            <option key={opt.value} value={opt.value} className="perspective-racing-input-bg perspective-racing-text-primary">{opt.label} ({opt.unit})</option>
          ))}
        </select>
      </div>
      <div className="flex-grow">
        {chartContent()}
      </div>
    </div>
  );
};

export default TimeChart;