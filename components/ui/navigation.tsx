import React from 'react';
import { NavLink } from 'react-router-dom';
import { cn } from '../../lib/utils';
import { Icons } from './icons';
import { ROUTE_PATHS } from '../../constants';

interface NavigationItem {
  path: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description?: string;
}

const navigationItems: NavigationItem[] = [
  {
    path: ROUTE_PATHS.DASHBOARD,
    label: 'Dashboard',
    icon: Icons.dashboard,
    description: 'Live performance metrics'
  },
  {
    path: ROUTE_PATHS.RACE_PLANNER,
    label: 'Race Planner',
    icon: Icons.racePlanner,
    description: 'Plan and manage races'
  },
  {
    path: ROUTE_PATHS.REPORTS,
    label: 'Reports',
    icon: Icons.reports,
    description: 'Performance analytics'
  },
  {
    path: ROUTE_PATHS.CREW,
    label: 'Crew',
    icon: Icons.crew,
    description: 'Team management'
  },
  {
    path: ROUTE_PATHS.SETTINGS,
    label: 'Settings',
    icon: Icons.settings,
    description: 'System configuration'
  }
];

interface NavigationProps {
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  showLabels?: boolean;
  showDescriptions?: boolean;
}

export function Navigation({ 
  className, 
  orientation = 'horizontal',
  showLabels = true,
  showDescriptions = false 
}: NavigationProps) {
  return (
    <nav className={cn(
      "flex",
      orientation === 'horizontal' ? "flex-row space-x-1" : "flex-col space-y-1",
      className
    )}>
      {navigationItems.map((item) => (
        <NavLink
          key={item.path}
          to={item.path}
          className={({ isActive }) => cn(
            "flex items-center gap-3 px-3 py-2 rounded-md text-sm font-medium transition-colors",
            "hover:bg-accent hover:text-accent-foreground",
            "focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
            isActive 
              ? "bg-primary text-primary-foreground" 
              : "text-muted-foreground",
            orientation === 'vertical' && "justify-start w-full",
            !showLabels && "justify-center"
          )}
        >
          <item.icon className={cn(
            "h-4 w-4 flex-shrink-0",
            !showLabels && "h-5 w-5"
          )} />
          {showLabels && (
            <div className="flex flex-col">
              <span>{item.label}</span>
              {showDescriptions && (
                <span className="text-xs opacity-70">{item.description}</span>
              )}
            </div>
          )}
        </NavLink>
      ))}
    </nav>
  );
}

// Mobile Navigation Component
interface MobileNavigationProps {
  isOpen: boolean;
  onClose: () => void;
}

export function MobileNavigation({ isOpen, onClose }: MobileNavigationProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 lg:hidden">
      <div className="fixed inset-0 bg-background/80 backdrop-blur-sm" onClick={onClose} />
      <div className="fixed left-0 top-0 h-full w-64 bg-card border-r shadow-lg">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-2">
            <Icons.ship className="h-6 w-6 text-primary" />
            <span className="text-lg font-semibold">
              <span className="text-foreground">Perspective</span>
              <span className="text-primary">Racing</span>
            </span>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-md hover:bg-accent"
          >
            <Icons.close className="h-4 w-4" />
          </button>
        </div>
        <div className="p-4">
          <Navigation 
            orientation="vertical" 
            showLabels={true}
            showDescriptions={true}
            className="space-y-2"
          />
        </div>
      </div>
    </div>
  );
}

// Breadcrumb Navigation
interface BreadcrumbItem {
  label: string;
  path?: string;
}

interface BreadcrumbProps {
  items: BreadcrumbItem[];
  className?: string;
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  return (
    <nav className={cn("flex items-center space-x-1 text-sm", className)}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <Icons.chevronRight className="h-4 w-4 text-muted-foreground" />
          )}
          {item.path ? (
            <NavLink
              to={item.path}
              className="text-muted-foreground hover:text-foreground transition-colors"
            >
              {item.label}
            </NavLink>
          ) : (
            <span className="text-foreground font-medium">{item.label}</span>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}
