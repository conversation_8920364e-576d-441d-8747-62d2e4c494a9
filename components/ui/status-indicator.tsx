import React from 'react';
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "../../lib/utils";
import { ConnectionIcons, PerformanceIcons } from "./icons";

const statusIndicatorVariants = cva(
  "inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium",
  {
    variants: {
      variant: {
        connected: "bg-green-500/20 text-green-400 border border-green-500/30",
        disconnected: "bg-red-500/20 text-red-400 border border-red-500/30",
        connecting: "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",
        excellent: "bg-green-500/20 text-green-400 border border-green-500/30",
        good: "bg-blue-500/20 text-blue-400 border border-blue-500/30",
        average: "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30",
        poor: "bg-red-500/20 text-red-400 border border-red-500/30",
        critical: "bg-red-600/20 text-red-300 border border-red-600/30",
      },
    },
    defaultVariants: {
      variant: "connected",
    },
  }
);

const statusDotVariants = cva(
  "w-2 h-2 rounded-full",
  {
    variants: {
      variant: {
        connected: "bg-green-400 animate-pulse",
        disconnected: "bg-red-400",
        connecting: "bg-yellow-400 animate-pulse",
        excellent: "bg-green-400",
        good: "bg-blue-400",
        average: "bg-yellow-400",
        poor: "bg-red-400",
        critical: "bg-red-600",
      },
    },
    defaultVariants: {
      variant: "connected",
    },
  }
);

interface StatusIndicatorProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof statusIndicatorVariants> {
  label: string;
  showIcon?: boolean;
  showDot?: boolean;
}

export function StatusIndicator({ 
  className, 
  variant, 
  label, 
  showIcon = true, 
  showDot = false,
  ...props 
}: StatusIndicatorProps) {
  const getIcon = () => {
    if (!showIcon) return null;
    
    switch (variant) {
      case 'connected':
        return <ConnectionIcons.connected className="h-4 w-4" />;
      case 'disconnected':
        return <ConnectionIcons.disconnected className="h-4 w-4" />;
      case 'connecting':
        return <ConnectionIcons.connecting className="h-4 w-4 animate-spin" />;
      case 'excellent':
        return <PerformanceIcons.excellent className="h-4 w-4" />;
      case 'good':
        return <PerformanceIcons.good className="h-4 w-4" />;
      case 'average':
        return <PerformanceIcons.average className="h-4 w-4" />;
      case 'poor':
        return <PerformanceIcons.poor className="h-4 w-4" />;
      case 'critical':
        return <PerformanceIcons.critical className="h-4 w-4" />;
      default:
        return null;
    }
  };

  return (
    <div
      className={cn(statusIndicatorVariants({ variant }), className)}
      {...props}
    >
      {showDot && <div className={statusDotVariants({ variant })} />}
      {getIcon()}
      <span>{label}</span>
    </div>
  );
}

// Connection Status Component
interface ConnectionStatusProps {
  isConnected: boolean;
  isConnecting?: boolean;
  label?: string;
  className?: string;
}

export function ConnectionStatus({ 
  isConnected, 
  isConnecting = false, 
  label,
  className 
}: ConnectionStatusProps) {
  const getVariant = () => {
    if (isConnecting) return 'connecting';
    return isConnected ? 'connected' : 'disconnected';
  };

  const getLabel = () => {
    if (label) return label;
    if (isConnecting) return 'Connecting';
    return isConnected ? 'Connected' : 'Disconnected';
  };

  return (
    <StatusIndicator
      variant={getVariant()}
      label={getLabel()}
      className={className}
      showDot
    />
  );
}

// Performance Status Component
interface PerformanceStatusProps {
  percentage: number;
  label?: string;
  className?: string;
}

export function PerformanceStatus({ 
  percentage, 
  label,
  className 
}: PerformanceStatusProps) {
  const getVariant = () => {
    if (percentage >= 100) return 'excellent';
    if (percentage >= 95) return 'good';
    if (percentage >= 90) return 'average';
    if (percentage >= 80) return 'poor';
    return 'critical';
  };

  const getLabel = () => {
    if (label) return label;
    if (percentage >= 100) return 'Excellent';
    if (percentage >= 95) return 'Good';
    if (percentage >= 90) return 'Average';
    if (percentage >= 80) return 'Poor';
    return 'Critical';
  };

  return (
    <StatusIndicator
      variant={getVariant()}
      label={`${getLabel()} (${percentage.toFixed(1)}%)`}
      className={className}
    />
  );
}
