import {
  Activity,
  AlertCircle,
  AlertTriangle,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUp,
  BarChart3,
  Calendar,
  CheckCircle,
  ChevronDown,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  Clock,
  Cloud,
  Compass,
  Download,
  Eye,
  EyeOff,
  FileText,
  Filter,
  Home,
  Info,
  Loader2,
  Map,
  MapPin,
  Menu,
  MoreHorizontal,
  Navigation,
  Play,
  Plus,
  Refresh,
  Save,
  Search,
  Settings,
  Ship,
  TrendingUp,
  Users,
  Wind,
  X,
  Zap,
  type LucideIcon,
} from "lucide-react"

export type Icon = LucideIcon

export const Icons = {
  // Navigation
  home: Home,
  dashboard: BarChart3,
  racePlanner: Map,
  reports: FileText,
  crew: Users,
  settings: Settings,
  
  // Actions
  play: Play,
  save: Save,
  download: Download,
  refresh: Refresh,
  search: Search,
  filter: Filter,
  menu: Menu,
  plus: Plus,
  close: X,
  
  // Arrows & Chevrons
  arrowUp: ArrowUp,
  arrowDown: ArrowDown,
  arrowLeft: ArrowLeft,
  arrowRight: ArrowRight,
  chevronUp: ChevronUp,
  chevronDown: ChevronDown,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  
  // Status & Alerts
  info: Info,
  success: CheckCircle,
  warning: AlertTriangle,
  error: AlertCircle,
  loading: Loader2,
  
  // Marine & Sailing
  ship: Ship,
  wind: Wind,
  compass: Compass,
  navigation: Navigation,
  
  // Data & Analytics
  activity: Activity,
  trending: TrendingUp,
  chart: BarChart3,
  
  // General
  calendar: Calendar,
  clock: Clock,
  cloud: Cloud,
  eye: Eye,
  eyeOff: EyeOff,
  mapPin: MapPin,
  more: MoreHorizontal,
  zap: Zap,
}

// Performance status icons
export const PerformanceIcons = {
  excellent: CheckCircle,
  good: TrendingUp,
  average: Activity,
  poor: AlertTriangle,
  critical: AlertCircle,
}

// Connection status icons
export const ConnectionIcons = {
  connected: Zap,
  disconnected: AlertCircle,
  connecting: Loader2,
}
