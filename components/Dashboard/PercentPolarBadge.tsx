
import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { PERFORMANCE_THRESHOLDS } from '../../constants';
import { Badge } from '../ui/Badge';
import { PerformanceStatus } from '../ui/status-indicator';

const PercentPolarBadge: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return (
      <Badge variant="outline" className="text-muted-foreground">
        No Data
      </Badge>
    );
  }

  const { percentPolar } = liveData;

  return (
    <div className="flex items-center space-x-2">
      <span className="text-xs text-muted-foreground hidden sm:inline">POLAR:</span>
      <PerformanceStatus
        percentage={percentPolar}
        className="text-sm font-semibold"
      />
    </div>
  );
};

export default PercentPolarBadge;
