import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { SeaStateType } from '../../types';
import { Badge } from '../ui/Badge';
import { Icons } from '../ui/icons';
import { cn } from '../../lib/utils';

interface MetricDisplayProps {
  label: string;
  value: string | number;
  unit?: string;
  className?: string;
  highlight?: boolean;
  icon?: React.ReactNode;
}

const MetricDisplay: React.FC<MetricDisplayProps> = ({
  label,
  value,
  unit,
  className = '',
  highlight = false,
  icon
}) => (
  <div className={cn(
    "p-4 rounded-lg bg-card border shadow-sm hover:shadow-md transition-all duration-200",
    highlight && "ring-2 ring-primary/20 bg-primary/5",
    className
  )}>
    <div className="flex items-center gap-2 mb-2">
      {icon}
      <div className="text-xs text-muted-foreground uppercase tracking-wider font-medium truncate">
        {label}
      </div>
    </div>
    <div className={cn(
      "text-2xl font-bold",
      highlight ? "text-primary" : "text-foreground"
    )}>
      {value}
      {unit && (
        <span className="text-sm font-normal ml-1.5 text-muted-foreground">
          {unit}
        </span>
      )}
    </div>
  </div>
);

const getSeaStateBadgeVariant = (seaState: SeaStateType) => {
  switch (seaState) {
    case SeaStateType.CALM: return 'success';
    case SeaStateType.MODERATE: return 'warning';
    case SeaStateType.ROUGH: return 'error';
    case SeaStateType.VERY_ROUGH: return 'destructive';
    default: return 'default';
  }
};

const LiveMetrics: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return (
      <div className="flex items-center justify-center py-10 text-muted-foreground">
        <Icons.loading className="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" />
        Waiting for live data
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <MetricDisplay
          label="Boat Speed"
          value={liveData.bsp.toFixed(1)}
          unit="kts"
          highlight
          icon={<Icons.zap className="h-4 w-4 text-primary" />}
        />
        <MetricDisplay
          label="VMG"
          value={liveData.vmg.toFixed(1)}
          unit="kts"
          highlight
          icon={<Icons.trending className="h-4 w-4 text-primary" />}
        />
        <MetricDisplay
          label="True Wind Speed"
          value={liveData.tws.toFixed(1)}
          unit="kts"
          icon={<Icons.wind className="h-4 w-4 text-blue-500" />}
        />
        <MetricDisplay
          label="True Wind Angle"
          value={liveData.twa.toFixed(0)}
          unit="°"
          icon={<Icons.compass className="h-4 w-4 text-blue-500" />}
        />

        <MetricDisplay
          label="Apparent Wind Speed"
          value={liveData.aws?.toFixed(1) ?? 'N/A'}
          unit="kts"
          icon={<Icons.wind className="h-4 w-4 text-slate-500" />}
        />
        <MetricDisplay
          label="Apparent Wind Angle"
          value={liveData.awa?.toFixed(0) ?? 'N/A'}
          unit="°"
          icon={<Icons.compass className="h-4 w-4 text-slate-500" />}
        />
        <MetricDisplay
          label="True Heading"
          value={liveData.heading?.toFixed(0) ?? 'N/A'}
          unit="°"
          icon={<Icons.navigation className="h-4 w-4 text-green-500" />}
        />
        <MetricDisplay
          label="Speed Over Ground"
          value={liveData.sog?.toFixed(1) ?? 'N/A'}
          unit="kts"
          icon={<Icons.activity className="h-4 w-4 text-green-500" />}
        />

        <MetricDisplay
          label="Course Over Ground"
          value={liveData.cog?.toFixed(0) ?? 'N/A'}
          unit="°"
          icon={<Icons.navigation className="h-4 w-4 text-green-500" />}
        />
        <MetricDisplay
          label="Heel Angle"
          value={liveData.heel.toFixed(1)}
          unit="°"
          icon={<Icons.activity className="h-4 w-4 text-orange-500" />}
        />
        <MetricDisplay
          label="Pitch Angle"
          value={liveData.pitch.toFixed(1)}
          unit="°"
          icon={<Icons.activity className="h-4 w-4 text-orange-500" />}
        />

        <div className="p-4 rounded-lg bg-card border shadow-sm hover:shadow-md transition-all duration-200">
          <div className="flex items-center gap-2 mb-2">
            <Icons.cloud className="h-4 w-4 text-blue-500" />
            <div className="text-xs text-muted-foreground uppercase tracking-wider font-medium">
              Sea State
            </div>
          </div>
          <div className="mt-2">
            <Badge variant={getSeaStateBadgeVariant(liveData.seaState)}>
              {liveData.seaState}
            </Badge>
          </div>
        </div>

        <MetricDisplay
          label="Depth"
          value={liveData.depth?.toFixed(1) ?? 'N/A'}
          unit="m"
          icon={<Icons.activity className="h-4 w-4 text-blue-600" />}
        />
      </div>

      <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
        <div className="flex items-center gap-2">
          <Icons.clock className="h-3 w-3" />
          Last updated: {new Date(liveData.utcTime).toLocaleTimeString()}
        </div>
        <div className="flex items-center gap-2">
          <Icons.zap className="h-3 w-3" />
          Live Data
        </div>
      </div>
    </div>
  );
};

export default LiveMetrics;