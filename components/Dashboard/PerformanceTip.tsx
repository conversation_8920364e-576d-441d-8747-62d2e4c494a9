import React, { useContext } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { PERFORMANCE_THRESHOLDS } from '../../constants';
import { Icons } from '../ui/icons';
import { PerformanceStatus } from '../ui/status-indicator';
import { cn } from '../../lib/utils';

const PerformanceTip: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return (
      <div className="flex items-center gap-2 text-muted-foreground">
        <Icons.loading className="h-4 w-4 animate-spin" />
        Awaiting performance data
      </div>
    );
  }

  const { tip, percentPolar, targetVMG, vmg } = liveData;

  const getPerformanceIcon = () => {
    if (percentPolar >= PERFORMANCE_THRESHOLDS.EXCELLENT) return Icons.success;
    if (percentPolar >= PERFORMANCE_THRESHOLDS.GOOD) return Icons.trending;
    if (percentPolar >= PERFORMANCE_THRESHOLDS.AVERAGE) return Icons.activity;
    return Icons.warning;
  };

  const getPerformanceColor = () => {
    if (percentPolar >= PERFORMANCE_THRESHOLDS.EXCELLENT) return 'text-green-400';
    if (percentPolar >= PERFORMANCE_THRESHOLDS.GOOD) return 'text-blue-400';
    if (percentPolar >= PERFORMANCE_THRESHOLDS.AVERAGE) return 'text-yellow-400';
    return 'text-red-400';
  };

  const PerformanceIcon = getPerformanceIcon();

  return (
    <div className="space-y-4">
      <div className="flex items-start gap-3 p-4 rounded-lg bg-card border">
        <PerformanceIcon className={cn("h-5 w-5 mt-0.5", getPerformanceColor())} />
        <div className="flex-1">
          <p className={cn("text-lg font-semibold", getPerformanceColor())}>
            {tip || "Maintain current performance"}
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Performance analysis based on current conditions
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        <div className="p-3 rounded-lg bg-card border">
          <div className="flex items-center gap-2 mb-1">
            <Icons.zap className="h-4 w-4 text-primary" />
            <span className="text-xs text-muted-foreground uppercase tracking-wider font-medium">
              Current VMG
            </span>
          </div>
          <span className="text-xl font-bold text-foreground">
            {vmg.toFixed(2)} <span className="text-sm font-normal text-muted-foreground">kts</span>
          </span>
        </div>

        <div className="p-3 rounded-lg bg-card border">
          <div className="flex items-center gap-2 mb-1">
            <Icons.trending className="h-4 w-4 text-blue-500" />
            <span className="text-xs text-muted-foreground uppercase tracking-wider font-medium">
              Target VMG
            </span>
          </div>
          <span className="text-xl font-bold text-foreground">
            {targetVMG.toFixed(2)} <span className="text-sm font-normal text-muted-foreground">kts</span>
          </span>
        </div>

        <div className="p-3 rounded-lg bg-card border">
          <div className="flex items-center gap-2 mb-1">
            <Icons.activity className="h-4 w-4 text-green-500" />
            <span className="text-xs text-muted-foreground uppercase tracking-wider font-medium">
              Performance
            </span>
          </div>
          <PerformanceStatus percentage={percentPolar} className="text-sm" />
        </div>
      </div>
    </div>
  );
};

export default PerformanceTip;