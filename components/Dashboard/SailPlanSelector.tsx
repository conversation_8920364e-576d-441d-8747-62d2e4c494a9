import React, { useContext, useState, useEffect } from 'react';
import { LiveDataContext } from '../../contexts/LiveDataContext';
import { SailPlan } from '../../types';
import { MOCK_SAIL_OPTIONS } from '../../constants';
import Select from '../ui/Select';
import { Button } from '../ui/Button';
import { Icons } from '../ui/icons';
import { cn } from '../../lib/utils';

const SailPlanSelector: React.FC = () => {
  const { sailPlan: currentSailPlan, updateSailPlan, loadingSailPlan, errorSailPlan } = useContext(LiveDataContext);
  const [localSailPlan, setLocalSailPlan] = useState<SailPlan>(currentSailPlan);
  const [isDirty, setIsDirty] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);

  useEffect(() => {
    setLocalSailPlan(currentSailPlan);
    setIsDirty(false);
  }, [currentSailPlan]);

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setLocalSailPlan(prev => ({ ...prev, [name]: value }));
    setIsDirty(true);
    setSubmitMessage(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitMessage(null);
    try {
      await updateSailPlan(localSailPlan);
      setIsDirty(false);
      setSubmitMessage({type: 'success', text: "Sail plan updated successfully!"});
      setTimeout(() => setSubmitMessage(null), 3000);
    } catch (error) {
      setSubmitMessage({type: 'error', text: `Error: ${errorSailPlan || 'Failed to update sail plan.'}`});
    } finally {
      setIsSubmitting(false);
    }
  };

  const headsailOptions = MOCK_SAIL_OPTIONS.HEADSAILS.map(s => ({ value: s, label: s }));
  const spinnakerOptions = MOCK_SAIL_OPTIONS.SPINNAKERS.map(s => ({ value: s, label: s }));
  const reefOptions = MOCK_SAIL_OPTIONS.REEFS.map(s => ({ value: s, label: s }));

  if (loadingSailPlan) {
    return (
      <div className="flex items-center justify-center py-6 text-muted-foreground">
        <Icons.loading className="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" />
        Loading sail plan
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <Select
        label="Headsail"
        name="headsail"
        id="headsail"
        value={localSailPlan.headsail}
        onChange={handleChange}
        options={headsailOptions}
      />
      <Select
        label="Spinnaker/Reaching Sail"
        name="spinnaker"
        id="spinnaker"
        value={localSailPlan.spinnaker}
        onChange={handleChange}
        options={spinnakerOptions}
      />
      <Select
        label="Reef (Mainsail)"
        name="reef"
        id="reef"
        value={localSailPlan.reef}
        onChange={handleChange}
        options={reefOptions}
      />
      <div className="pt-2">
        <Button
          type="submit"
          disabled={!isDirty || isSubmitting}
          isLoading={isSubmitting}
          className="w-full"
          size="lg"
        >
          <Icons.save className="h-4 w-4 mr-2" />
          {isSubmitting ? 'Updating' : 'Update Sail Plan'}
        </Button>
      </div>

      {submitMessage && (
        <div className={cn(
          "text-sm mt-3 p-3 rounded-lg border",
          submitMessage.type === 'error'
            ? 'bg-destructive/10 text-destructive border-destructive/20'
            : 'bg-green-500/10 text-green-400 border-green-500/20'
        )}>
          <div className="flex items-center gap-2">
            {submitMessage.type === 'error' ? (
              <Icons.error className="h-4 w-4" />
            ) : (
              <Icons.success className="h-4 w-4" />
            )}
            {submitMessage.text}
          </div>
        </div>
      )}
    </form>
  );
};

export default SailPlanSelector;