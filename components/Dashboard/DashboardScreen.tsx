
import React from 'react';
import LiveMetrics from './LiveMetrics';
import SailPlanSelector from './SailPlanSelector';
import PerformanceTip from './PerformanceTip';
import Card, { CardHeader, CardTitle, CardContent } from '../ui/Card';
import { Icons } from '../ui/icons';

const DashboardScreen: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icons.activity className="h-5 w-5 text-primary" />
              Live Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <LiveMetrics />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Icons.ship className="h-5 w-5 text-primary" />
              Sail Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <SailPlanSelector />
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Icons.trending className="h-5 w-5 text-primary" />
            Performance Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <PerformanceTip />
        </CardContent>
      </Card>
    </div>
  );
};

export default DashboardScreen;
