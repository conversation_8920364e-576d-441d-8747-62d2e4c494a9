// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model PolarTable {
  id          String       @id @default(cuid())
  name        String       @unique
  description String?
  isActive    Boolean      @default(false)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  entries     PolarEntry[]
}

model PolarEntry {
  id              String     @id @default(cuid())
  tws             Float      // True Wind Speed
  twa             Float      // True Wind Angle
  targetSpeed     Float      // Target boat speed
  sailConfiguration String?  // Optional sail config
  polarTableId    String
  polarTable      PolarTable @relation(fields: [polarTableId], references: [id], onDelete: Cascade)

  @@unique([polarTableId, tws, twa])
}

model RaceEvent {
  id          String            @id @default(cuid())
  name        String
  startTime   DateTime
  endTime     DateTime?
  notes       String?
  route       String?           // JSON string for waypoints
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  rsvps       CrewRSVP[]
  performances PerformanceData[]
  maneuvers   TackJibeManeuver[]
}

model CrewMember {
  id        String     @id @default(cuid())
  name      String
  role      String?
  email     String?    @unique
  phone     String?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  rsvps     CrewRSVP[]
}

model CrewRSVP {
  id           String      @id @default(cuid())
  status       String      // 'Attending' | 'Not Attending' | 'Maybe'
  notes        String?
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  raceId       String
  crewMemberId String
  race         RaceEvent   @relation(fields: [raceId], references: [id], onDelete: Cascade)
  crewMember   CrewMember  @relation(fields: [crewMemberId], references: [id], onDelete: Cascade)

  @@unique([raceId, crewMemberId])
}

model PerformanceData {
  id          String    @id @default(cuid())
  timestamp   DateTime  @default(now())
  bsp         Float     // Boat Speed
  tws         Float     // True Wind Speed
  twa         Float     // True Wind Angle
  vmg         Float     // Velocity Made Good
  targetVMG   Float     // Target VMG from polars
  percentPolar Float    // Performance percentage
  seaState    String    // Sea state enum
  tip         String?   // Performance tip
  heel        Float?    // Heel angle
  pitch       Float?    // Pitch angle
  roll        Float?    // Roll angle
  lat         Float?    // Latitude
  lon         Float?    // Longitude
  heading     Float?    // True heading
  sog         Float?    // Speed Over Ground
  cog         Float?    // Course Over Ground
  depth       Float?    // Depth
  aws         Float?    // Apparent Wind Speed
  awa         Float?    // Apparent Wind Angle
  raceId      String?
  race        RaceEvent? @relation(fields: [raceId], references: [id], onDelete: SetNull)

  @@index([timestamp])
  @@index([raceId])
}

model SailPlan {
  id        String   @id @default(cuid())
  headsail  String
  spinnaker String
  reef      String
  isActive  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model TackJibeManeuver {
  id                    String    @id @default(cuid())
  type                  String    // 'Tack' | 'Jibe'
  startTime             DateTime
  endTime               DateTime
  durationSeconds       Float
  distanceLostMeters    Float?
  entrySpeed            Float
  exitSpeed             Float
  avgSpeedDuringManeuver Float
  raceId                String?
  race                  RaceEvent? @relation(fields: [raceId], references: [id], onDelete: SetNull)
  createdAt             DateTime  @default(now())

  @@index([startTime])
  @@index([raceId])
}

model Settings {
  id    String @id @default(cuid())
  key   String @unique
  value String  // JSON string for complex values
  updatedAt DateTime @updatedAt
}
