#!/bin/bash

# Perspective Racing Deployment Script
# This script helps deploy the application to various environments

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ is required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    npm install
    print_success "Dependencies installed"
}

# Function to setup database
setup_database() {
    print_status "Setting up database..."
    
    # Generate Prisma client
    npx prisma generate
    
    # Push database schema
    npx prisma db push
    
    print_success "Database setup completed"
}

# Function to build application
build_application() {
    print_status "Building application..."
    
    # Build frontend
    npm run build:client
    
    # Build backend
    npm run build:server
    
    print_success "Application built successfully"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    
    if npm run test; then
        print_success "All tests passed"
    else
        print_warning "Some tests failed, but continuing deployment"
    fi
}

# Function to deploy locally
deploy_local() {
    print_status "Deploying locally..."
    
    check_prerequisites
    install_dependencies
    setup_database
    
    print_success "Local deployment completed"
    print_status "You can now run 'npm run dev' to start the development server"
}

# Function to deploy production
deploy_production() {
    print_status "Deploying for production..."
    
    check_prerequisites
    install_dependencies
    setup_database
    run_tests
    build_application
    
    print_success "Production build completed"
    print_status "You can now run 'npm start' to start the production server"
}

# Function to deploy with Docker
deploy_docker() {
    print_status "Deploying with Docker..."
    
    if ! command_exists docker; then
        print_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    # Build Docker image
    print_status "Building Docker image..."
    docker build -t perspective-racing .
    
    print_success "Docker image built successfully"
    print_status "You can now run 'docker-compose up -d' to start the application"
}

# Function to deploy to Raspberry Pi
deploy_raspberry_pi() {
    print_status "Setting up for Raspberry Pi deployment..."
    
    check_prerequisites
    install_dependencies
    setup_database
    build_application
    
    # Install PM2 if not present
    if ! command_exists pm2; then
        print_status "Installing PM2 for process management..."
        npm install -g pm2
    fi
    
    # Create PM2 ecosystem file
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'perspective-racing',
    script: 'dist/server/index.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 8080
    }
  }]
};
EOF
    
    print_success "Raspberry Pi deployment setup completed"
    print_status "Run 'pm2 start ecosystem.config.js' to start the application"
    print_status "Run 'pm2 save && pm2 startup' to enable auto-start on boot"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTION]"
    echo ""
    echo "Options:"
    echo "  local       Deploy for local development"
    echo "  production  Deploy for production"
    echo "  docker      Deploy with Docker"
    echo "  pi          Deploy for Raspberry Pi"
    echo "  test        Run tests only"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 local       # Setup for local development"
    echo "  $0 production  # Build for production"
    echo "  $0 docker      # Build Docker image"
    echo "  $0 pi          # Setup for Raspberry Pi"
}

# Main script logic
case "${1:-}" in
    "local")
        deploy_local
        ;;
    "production")
        deploy_production
        ;;
    "docker")
        deploy_docker
        ;;
    "pi")
        deploy_raspberry_pi
        ;;
    "test")
        check_prerequisites
        install_dependencies
        run_tests
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    "")
        print_error "No deployment option specified"
        show_usage
        exit 1
        ;;
    *)
        print_error "Unknown option: $1"
        show_usage
        exit 1
        ;;
esac
