import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(value: number, decimals: number = 1): string {
  return value.toFixed(decimals)
}

export function formatPercentage(value: number, decimals: number = 1): string {
  return `${value.toFixed(decimals)}%`
}

export function formatTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString()
}

export function formatDateTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return `${formatDate(d)} ${formatTime(d)}`
}

export function getPerformanceColor(percentage: number): string {
  if (percentage >= 100) return 'text-green-400'
  if (percentage >= 95) return 'text-blue-400'
  if (percentage >= 90) return 'text-yellow-400'
  return 'text-red-400'
}

export function getPerformanceBadgeVariant(percentage: number): 'success' | 'info' | 'warning' | 'error' {
  if (percentage >= 100) return 'success'
  if (percentage >= 95) return 'info'
  if (percentage >= 90) return 'warning'
  return 'error'
}
