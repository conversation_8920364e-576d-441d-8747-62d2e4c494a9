@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 8%;
    --foreground: 210 40% 98%;
    --card: 210 40% 12%;
    --card-foreground: 210 40% 98%;
    --popover: 210 40% 12%;
    --popover-foreground: 210 40% 98%;
    --primary: 200 98% 39%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 16%;
    --secondary-foreground: 210 40% 98%;
    --muted: 210 40% 16%;
    --muted-foreground: 210 40% 65%;
    --accent: 210 40% 16%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 210 40% 18%;
    --input: 210 40% 18%;
    --ring: 200 98% 39%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-inter;
    font-feature-settings: "rlig" 1, "calt" 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--primary));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.8);
}

/* For Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--primary)) hsl(var(--muted));
}

/* Perspective Racing specific styles */
.perspective-racing-app-bg {
  background: linear-gradient(135deg, hsl(var(--background)) 0%, hsl(210 40% 6%) 100%);
}

.perspective-racing-card-bg {
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
}

.perspective-racing-input-bg {
  background: hsl(var(--input));
  border: 1px solid hsl(var(--border));
}

.perspective-racing-border {
  border-color: hsl(var(--border));
}

.perspective-racing-text-primary {
  color: hsl(var(--foreground));
}

.perspective-racing-text-secondary {
  color: hsl(var(--muted-foreground));
}

.perspective-racing-accent-sky {
  color: hsl(var(--primary));
}

.perspective-racing-accent-teal {
  color: 174 100% 47%;
}

.perspective-racing-focus-ring {
  ring-color: hsl(var(--ring));
}

/* Performance badge styles */
.performance-excellent {
  @apply bg-green-500/20 text-green-400 border-green-500/30;
}

.performance-good {
  @apply bg-blue-500/20 text-blue-400 border-blue-500/30;
}

.performance-average {
  @apply bg-yellow-500/20 text-yellow-400 border-yellow-500/30;
}

.performance-poor {
  @apply bg-red-500/20 text-red-400 border-red-500/30;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Leaflet map container */
.leaflet-container {
  height: 100%;
  width: 100%;
  border-radius: 0.5rem;
  background: hsl(var(--muted));
}

/* Recharts tooltip styling */
.recharts-tooltip-wrapper {
  z-index: 9999 !important;
}

.recharts-tooltip-content {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem !important;
  color: hsl(var(--popover-foreground)) !important;
}

/* Loading spinner */
.loading-spinner {
  @apply animate-spin h-8 w-8 text-primary;
}

/* Connection status indicator */
.connection-indicator {
  @apply inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium;
}

.connection-connected {
  @apply bg-green-500/20 text-green-400 border border-green-500/30;
}

.connection-disconnected {
  @apply bg-red-500/20 text-red-400 border border-red-500/30;
}

.connection-connecting {
  @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
}

/* Status dot animation */
.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot-connected {
  @apply bg-green-400;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-dot-disconnected {
  @apply bg-red-400;
}

.status-dot-connecting {
  @apply bg-yellow-400;
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
  .perspective-racing-card-bg {
    @apply mx-2;
  }

  .leaflet-container {
    height: 300px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  .perspective-racing-app-bg {
    background: white !important;
    color: black !important;
  }

  .perspective-racing-card-bg {
    background: white !important;
    border: 1px solid #ccc !important;
  }
}
