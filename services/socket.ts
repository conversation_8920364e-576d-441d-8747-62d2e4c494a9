
import { io, Socket } from 'socket.io-client';
import { LiveData, SailPlan } from '../types.js';

class SocketService {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;

  constructor() {
    this.connect();
  }

  private connect(): void {
    const serverUrl = import.meta.env.VITE_SERVER_URL || 'http://localhost:8080';

    this.socket = io(serverUrl, {
      transports: ['websocket', 'polling'],
      timeout: 10000,
      forceNew: true
    });

    this.socket.on('connect', () => {
      console.log('✅ Connected to server');
      this.isConnected = true;
      this.reconnectAttempts = 0;

      // Subscribe to live data by default
      this.socket?.emit('subscribe:liveData');
    });

    this.socket.on('disconnect', (reason) => {
      console.log('🔌 Disconnected from server:', reason);
      this.isConnected = false;

      if (reason === 'io server disconnect') {
        // Server initiated disconnect, reconnect manually
        this.handleReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ Connection error:', error);
      this.isConnected = false;
      this.handleReconnect();
    });

    this.socket.on('error', (error) => {
      console.error('❌ Socket error:', error);
    });
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

      console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms...`);

      setTimeout(() => {
        this.socket?.connect();
      }, delay);
    } else {
      console.error('❌ Max reconnection attempts reached');
    }
  }

  // Event listeners
  on(event: string, callback: (data: any) => void): void {
    this.socket?.on(event, callback);
  }

  off(event: string, callback?: (data: any) => void): void {
    this.socket?.off(event, callback);
  }

  // Emit events
  emit(event: string, data?: any): void {
    if (this.isConnected) {
      this.socket?.emit(event, data);
    } else {
      console.warn('⚠️ Cannot emit event - not connected to server');
    }
  }

  // Specific methods for the application
  subscribeLiveData(callback: (data: LiveData) => void): void {
    this.on('liveUpdate', callback);
    this.emit('subscribe:liveData');
  }

  unsubscribeLiveData(): void {
    this.emit('unsubscribe:liveData');
    this.off('liveUpdate');
  }

  subscribeRace(raceId: string, callback?: (data: any) => void): void {
    this.emit('subscribe:race', raceId);
    if (callback) {
      this.on(`race:${raceId}`, callback);
    }
  }

  unsubscribeRace(raceId: string): void {
    this.emit('unsubscribe:race', raceId);
    this.off(`race:${raceId}`);
  }

  updateSailPlan(sailPlan: SailPlan): void {
    this.emit('sailPlan:update', sailPlan);
  }

  startRace(raceId: string): void {
    this.emit('race:start', raceId);
  }

  finishRace(raceId: string): void {
    this.emit('race:finish', raceId);
  }

  storePerformanceData(data: LiveData): void {
    this.emit('performance:store', data);
  }

  requestStatus(callback: (status: any) => void): void {
    this.on('status:response', callback);
    this.emit('status:request');
  }

  ping(): void {
    this.emit('ping');
  }

  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  disconnect(): void {
    this.socket?.disconnect();
    this.isConnected = false;
  }
}

// Create singleton instance
const socketService = new SocketService();

export default socketService;
