# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Production builds
dist/
dist-ssr/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Database
*.db
*.db-journal
dev.db*
production.db*

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# parcel-bundler cache
.cache
.parcel-cache

# Serverless directories
.serverless

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.swp
*.swo
*~

# OS generated files
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/

# Backup files
backups/
*.backup

# Test files
test-results/
playwright-report/
test-results.xml

# Prisma
prisma/migrations/
!prisma/migrations/.gitkeep

# Local data
data/
*.sqlite
*.sqlite3
