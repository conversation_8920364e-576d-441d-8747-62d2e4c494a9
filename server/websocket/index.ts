import { Server as SocketIOServer, Socket } from 'socket.io';
import { PrismaClient } from '@prisma/client';
import { NMEAListener } from '../services/nmea-listener.js';
import { LiveData } from '../../types.js';

interface ClientInfo {
  id: string;
  connectedAt: Date;
  lastSeen: Date;
  subscriptions: Set<string>;
}

export function setupWebSocket(
  io: SocketIOServer,
  prisma: PrismaClient,
  nmeaListener: NMEAListener
): void {
  const connectedClients = new Map<string, ClientInfo>();
  let currentRaceId: string | null = null;

  // Handle client connections
  io.on('connection', (socket: Socket) => {
    const clientId = socket.id;
    console.log(`🔌 Client connected: ${clientId}`);

    // Store client info
    connectedClients.set(clientId, {
      id: clientId,
      connectedAt: new Date(),
      lastSeen: new Date(),
      subscriptions: new Set()
    });

    // Send connection acknowledgment
    socket.emit('connected', {
      clientId,
      serverTime: new Date().toISOString(),
      nmeaStatus: nmeaListener.isListening() ? 'connected' : 'disconnected'
    });

    // Handle live data subscription
    socket.on('subscribe:liveData', () => {
      const client = connectedClients.get(clientId);
      if (client) {
        client.subscriptions.add('liveData');
        client.lastSeen = new Date();
        console.log(`📊 Client ${clientId} subscribed to live data`);
      }
    });

    // Handle live data unsubscription
    socket.on('unsubscribe:liveData', () => {
      const client = connectedClients.get(clientId);
      if (client) {
        client.subscriptions.delete('liveData');
        client.lastSeen = new Date();
        console.log(`📊 Client ${clientId} unsubscribed from live data`);
      }
    });

    // Handle race subscription
    socket.on('subscribe:race', (raceId: string) => {
      const client = connectedClients.get(clientId);
      if (client) {
        client.subscriptions.add(`race:${raceId}`);
        client.lastSeen = new Date();
        socket.join(`race:${raceId}`);
        console.log(`🏁 Client ${clientId} subscribed to race ${raceId}`);
      }
    });

    // Handle race unsubscription
    socket.on('unsubscribe:race', (raceId: string) => {
      const client = connectedClients.get(clientId);
      if (client) {
        client.subscriptions.delete(`race:${raceId}`);
        client.lastSeen = new Date();
        socket.leave(`race:${raceId}`);
        console.log(`🏁 Client ${clientId} unsubscribed from race ${raceId}`);
      }
    });

    // Handle sail plan updates
    socket.on('sailPlan:update', async (sailPlan: { headsail: string; spinnaker: string; reef: string }) => {
      try {
        // Update sail plan in database
        await prisma.$transaction(async (tx) => {
          await tx.sailPlan.updateMany({
            where: { isActive: true },
            data: { isActive: false }
          });

          await tx.sailPlan.create({
            data: {
              headsail: sailPlan.headsail,
              spinnaker: sailPlan.spinnaker,
              reef: sailPlan.reef,
              isActive: true
            }
          });
        });

        // Broadcast to all clients
        io.emit('sailPlan:updated', sailPlan);
        console.log(`⛵ Sail plan updated by client ${clientId}:`, sailPlan);
      } catch (error) {
        console.error('Error updating sail plan:', error);
        socket.emit('error', { message: 'Failed to update sail plan' });
      }
    });

    // Handle race start
    socket.on('race:start', async (raceId: string) => {
      try {
        const race = await prisma.raceEvent.update({
          where: { id: raceId },
          data: { startTime: new Date() }
        });

        currentRaceId = raceId;
        io.to(`race:${raceId}`).emit('race:started', {
          raceId,
          startTime: race.startTime
        });
        console.log(`🏁 Race ${raceId} started by client ${clientId}`);
      } catch (error) {
        console.error('Error starting race:', error);
        socket.emit('error', { message: 'Failed to start race' });
      }
    });

    // Handle race finish
    socket.on('race:finish', async (raceId: string) => {
      try {
        const race = await prisma.raceEvent.update({
          where: { id: raceId },
          data: { endTime: new Date() }
        });

        if (currentRaceId === raceId) {
          currentRaceId = null;
        }

        io.to(`race:${raceId}`).emit('race:finished', {
          raceId,
          endTime: race.endTime
        });
        console.log(`🏁 Race ${raceId} finished by client ${clientId}`);
      } catch (error) {
        console.error('Error finishing race:', error);
        socket.emit('error', { message: 'Failed to finish race' });
      }
    });

    // Handle performance data storage request
    socket.on('performance:store', async (data: LiveData) => {
      try {
        await prisma.performanceData.create({
          data: {
            bsp: data.bsp,
            tws: data.tws,
            twa: data.twa,
            vmg: data.vmg,
            targetVMG: data.targetVMG,
            percentPolar: data.percentPolar,
            seaState: data.seaState,
            tip: data.tip,
            heel: data.heel,
            pitch: data.pitch,
            roll: data.roll,
            lat: data.lat,
            lon: data.lon,
            heading: data.heading,
            sog: data.sog,
            cog: data.cog,
            depth: data.depth,
            aws: data.aws,
            awa: data.awa,
            raceId: currentRaceId
          }
        });

        socket.emit('performance:stored', { success: true });
      } catch (error) {
        console.error('Error storing performance data:', error);
        socket.emit('performance:stored', { success: false, error: 'Failed to store data' });
      }
    });

    // Handle ping/pong for connection health
    socket.on('ping', () => {
      const client = connectedClients.get(clientId);
      if (client) {
        client.lastSeen = new Date();
      }
      socket.emit('pong', { timestamp: new Date().toISOString() });
    });

    // Handle client status request
    socket.on('status:request', () => {
      socket.emit('status:response', {
        nmeaConnected: nmeaListener.isListening(),
        lastDataTime: nmeaListener.getLastDataTime(),
        currentRace: currentRaceId,
        connectedClients: connectedClients.size,
        serverTime: new Date().toISOString()
      });
    });

    // Handle disconnection
    socket.on('disconnect', (reason) => {
      console.log(`🔌 Client disconnected: ${clientId} (${reason})`);
      connectedClients.delete(clientId);
    });

    // Handle connection errors
    socket.on('error', (error) => {
      console.error(`❌ Socket error for client ${clientId}:`, error);
    });
  });

  // Broadcast live data to subscribed clients
  const broadcastLiveData = (data: LiveData) => {
    const subscribedClients = Array.from(connectedClients.entries())
      .filter(([_, client]) => client.subscriptions.has('liveData'))
      .map(([id, _]) => id);

    if (subscribedClients.length > 0) {
      io.to(subscribedClients).emit('liveUpdate', data);
    }

    // Store performance data if auto-store is enabled and we have a current race
    if (currentRaceId) {
      prisma.performanceData.create({
        data: {
          bsp: data.bsp,
          tws: data.tws,
          twa: data.twa,
          vmg: data.vmg,
          targetVMG: data.targetVMG,
          percentPolar: data.percentPolar,
          seaState: data.seaState,
          tip: data.tip,
          heel: data.heel,
          pitch: data.pitch,
          roll: data.roll,
          lat: data.lat,
          lon: data.lon,
          heading: data.heading,
          sog: data.sog,
          cog: data.cog,
          depth: data.depth,
          aws: data.aws,
          awa: data.awa,
          raceId: currentRaceId
        }
      }).catch(error => {
        console.error('Error auto-storing performance data:', error);
      });
    }
  };

  // Listen for live data from NMEA listener
  io.on('liveUpdate', broadcastLiveData);

  // Periodic cleanup of stale clients
  setInterval(() => {
    const now = new Date();
    const staleThreshold = 5 * 60 * 1000; // 5 minutes

    for (const [clientId, client] of connectedClients.entries()) {
      if (now.getTime() - client.lastSeen.getTime() > staleThreshold) {
        console.log(`🧹 Cleaning up stale client: ${clientId}`);
        connectedClients.delete(clientId);
      }
    }
  }, 60000); // Check every minute

  // Periodic status broadcast
  setInterval(() => {
    io.emit('status:broadcast', {
      nmeaConnected: nmeaListener.isListening(),
      lastDataTime: nmeaListener.getLastDataTime(),
      currentRace: currentRaceId,
      connectedClients: connectedClients.size,
      serverTime: new Date().toISOString()
    });
  }, 30000); // Every 30 seconds

  console.log('🌐 WebSocket server configured');
}
