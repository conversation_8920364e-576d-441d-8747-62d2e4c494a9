import { PolarEngine } from './polar-engine.js';
import { SeaStateType } from '../../types.js';

interface PerformanceResult {
  targetVMG: number;
  percentPolar: number;
  tip: string;
}

interface SmoothingWindow {
  bsp: number[];
  tws: number[];
  twa: number[];
  timestamps: number[];
}

export class PerformanceCalculator {
  private smoothingWindow: SmoothingWindow = {
    bsp: [],
    tws: [],
    twa: [],
    timestamps: []
  };
  
  private readonly WINDOW_SIZE = 10; // Number of data points to smooth over
  private readonly WINDOW_TIME_MS = 30000; // 30 seconds max age for smoothing

  constructor(private polarEngine: PolarEngine) {}

  calculatePerformance(
    bsp: number,
    tws: number,
    twa: number,
    seaState: SeaStateType
  ): PerformanceResult {
    // Add to smoothing window
    this.addToSmoothingWindow(bsp, tws, twa);

    // Get smoothed values
    const smoothedBSP = this.getSmoothedValue(this.smoothingWindow.bsp);
    const smoothedTWS = this.getSmoothedValue(this.smoothingWindow.tws);
    const smoothedTWA = this.getSmoothedValue(this.smoothingWindow.twa);

    // Get target from polar
    const polar = this.polarEngine.getTargetSpeed(smoothedTWS, smoothedTWA, seaState);
    
    // Calculate actual VMG
    const actualVMG = smoothedBSP * Math.cos((smoothedTWA * Math.PI) / 180);
    
    // Calculate performance percentage
    const percentPolar = polar.targetVMG > 0 ? (actualVMG / polar.targetVMG) * 100 : 0;
    
    // Generate performance tip
    const tip = this.generatePerformanceTip(
      smoothedBSP,
      smoothedTWS,
      smoothedTWA,
      polar.targetSpeed,
      percentPolar,
      seaState
    );

    return {
      targetVMG: polar.targetVMG,
      percentPolar: Math.round(percentPolar * 10) / 10,
      tip
    };
  }

  private addToSmoothingWindow(bsp: number, tws: number, twa: number): void {
    const now = Date.now();
    
    // Add new values
    this.smoothingWindow.bsp.push(bsp);
    this.smoothingWindow.tws.push(tws);
    this.smoothingWindow.twa.push(twa);
    this.smoothingWindow.timestamps.push(now);

    // Remove old values (by count)
    if (this.smoothingWindow.bsp.length > this.WINDOW_SIZE) {
      this.smoothingWindow.bsp.shift();
      this.smoothingWindow.tws.shift();
      this.smoothingWindow.twa.shift();
      this.smoothingWindow.timestamps.shift();
    }

    // Remove old values (by time)
    const cutoffTime = now - this.WINDOW_TIME_MS;
    while (this.smoothingWindow.timestamps.length > 0 && 
           this.smoothingWindow.timestamps[0] < cutoffTime) {
      this.smoothingWindow.bsp.shift();
      this.smoothingWindow.tws.shift();
      this.smoothingWindow.twa.shift();
      this.smoothingWindow.timestamps.shift();
    }
  }

  private getSmoothedValue(values: number[]): number {
    if (values.length === 0) return 0;
    
    // Use exponential moving average for more recent values
    let weightedSum = 0;
    let totalWeight = 0;
    
    for (let i = 0; i < values.length; i++) {
      const weight = Math.exp(i / values.length); // More weight to recent values
      weightedSum += values[i] * weight;
      totalWeight += weight;
    }
    
    return totalWeight > 0 ? weightedSum / totalWeight : 0;
  }

  private generatePerformanceTip(
    bsp: number,
    tws: number,
    twa: number,
    targetSpeed: number,
    percentPolar: number,
    seaState: SeaStateType
  ): string {
    // Performance thresholds
    if (percentPolar >= 100) {
      return "Excellent! On or above target speed";
    }
    
    if (percentPolar >= 95) {
      return "Very good performance, minor tweaks possible";
    }
    
    if (percentPolar >= 90) {
      return "Good speed, room for improvement";
    }
    
    if (percentPolar >= 80) {
      return this.getSpecificTip(bsp, tws, twa, targetSpeed, seaState);
    }
    
    return this.getCriticalTip(bsp, tws, twa, targetSpeed, seaState);
  }

  private getSpecificTip(
    bsp: number,
    tws: number,
    twa: number,
    targetSpeed: number,
    seaState: SeaStateType
  ): string {
    const speedDeficit = targetSpeed - bsp;
    
    // Analyze sailing angle
    if (twa < 45) {
      if (speedDeficit > 1) {
        return "Too close to wind - bear away for more speed";
      }
      return "Consider bearing away slightly for better VMG";
    }
    
    if (twa > 45 && twa < 60) {
      if (speedDeficit > 0.5) {
        return "Check sail trim - may need to ease sheets";
      }
      return "Good angle, fine-tune sail trim";
    }
    
    if (twa > 60 && twa < 90) {
      if (speedDeficit > 0.5) {
        return "Check for proper sail shape and twist";
      }
      return "Optimize sail trim for reaching";
    }
    
    if (twa > 90 && twa < 120) {
      if (speedDeficit > 0.5) {
        return "Consider spinnaker if not already set";
      }
      return "Good broad reach angle";
    }
    
    if (twa > 120 && twa < 150) {
      if (speedDeficit > 0.5) {
        return "Check for proper downwind sail configuration";
      }
      return "Consider jibing for better VMG";
    }
    
    if (twa > 150) {
      return "Deep running - consider jibing for better VMG";
    }
    
    return "Check sail trim and boat balance";
  }

  private getCriticalTip(
    bsp: number,
    tws: number,
    twa: number,
    targetSpeed: number,
    seaState: SeaStateType
  ): string {
    const speedDeficit = targetSpeed - bsp;
    
    if (speedDeficit > 2) {
      if (twa < 50) {
        return "CRITICAL: Bear away significantly - too close to wind";
      }
      if (twa > 140) {
        return "CRITICAL: Consider jibing - poor VMG angle";
      }
      return "CRITICAL: Check for major sail trim or boat handling issues";
    }
    
    if (twa < 40) {
      return "Pinching - bear away for speed";
    }
    
    if (twa > 160) {
      return "Running by the lee - jibe or head up";
    }
    
    if (seaState === SeaStateType.ROUGH || seaState === SeaStateType.VERY_ROUGH) {
      return "Rough conditions - focus on boat handling over speed";
    }
    
    if (tws < 8) {
      return "Light air - check for proper light air sails";
    }
    
    if (tws > 20) {
      return "Heavy air - consider reefing if not already done";
    }
    
    return "Significant speed loss - check all systems";
  }

  // Calculate optimal VMG angle for current conditions
  calculateOptimalVMGAngle(tws: number, isUpwind: boolean = true): number {
    const testAngles = isUpwind 
      ? [35, 40, 45, 50, 55, 60] 
      : [120, 130, 140, 150, 160, 170];
    
    let bestAngle = testAngles[0];
    let bestVMG = 0;
    
    for (const angle of testAngles) {
      const polar = this.polarEngine.getTargetSpeed(tws, angle);
      if (polar.targetVMG > bestVMG) {
        bestVMG = polar.targetVMG;
        bestAngle = angle;
      }
    }
    
    return bestAngle;
  }

  // Calculate layline information
  calculateLaylines(
    currentLat: number,
    currentLon: number,
    markLat: number,
    markLon: number,
    tws: number
  ): { port: number; starboard: number } {
    // Calculate bearing to mark
    const bearing = this.calculateBearing(currentLat, currentLon, markLat, markLon);
    
    // Get optimal upwind angle
    const optimalAngle = this.calculateOptimalVMGAngle(tws, true);
    
    // Calculate layline bearings
    const portLayline = (bearing - optimalAngle + 360) % 360;
    const starboardLayline = (bearing + optimalAngle) % 360;
    
    return {
      port: portLayline,
      starboard: starboardLayline
    };
  }

  private calculateBearing(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const lat1Rad = lat1 * Math.PI / 180;
    const lat2Rad = lat2 * Math.PI / 180;
    
    const y = Math.sin(dLon) * Math.cos(lat2Rad);
    const x = Math.cos(lat1Rad) * Math.sin(lat2Rad) - 
              Math.sin(lat1Rad) * Math.cos(lat2Rad) * Math.cos(dLon);
    
    const bearing = Math.atan2(y, x) * 180 / Math.PI;
    return (bearing + 360) % 360;
  }

  // Get current smoothing window stats
  getSmoothingStats(): {
    windowSize: number;
    maxWindowSize: number;
    oldestDataAge: number;
  } {
    const now = Date.now();
    const oldestTimestamp = this.smoothingWindow.timestamps[0] || now;
    
    return {
      windowSize: this.smoothingWindow.bsp.length,
      maxWindowSize: this.WINDOW_SIZE,
      oldestDataAge: now - oldestTimestamp
    };
  }

  // Clear smoothing window (useful for race starts)
  clearSmoothingWindow(): void {
    this.smoothingWindow = {
      bsp: [],
      tws: [],
      twa: [],
      timestamps: []
    };
  }
}
