import { PrismaClient } from '@prisma/client';
import { PolarTable, PolarEntry, SeaStateType } from '../../types.js';

interface PolarPoint {
  tws: number;
  twa: number;
  targetSpeed: number;
}

interface InterpolatedPolar {
  targetSpeed: number;
  targetVMG: number;
}

export class PolarEngine {
  private polarCache: Map<string, PolarPoint[]> = new Map();
  private activePolarId: string | null = null;

  constructor(private prisma: PrismaClient) {
    this.loadActivePolar();
  }

  async loadActivePolar(): Promise<void> {
    try {
      const activePolar = await this.prisma.polarTable.findFirst({
        where: { isActive: true },
        include: { entries: true }
      });

      if (activePolar) {
        this.activePolarId = activePolar.id;
        const points = activePolar.entries.map(entry => ({
          tws: entry.tws,
          twa: entry.twa,
          targetSpeed: entry.targetSpeed
        }));
        this.polarCache.set(activePolar.id, points);
        console.log(`📊 Loaded active polar table: ${activePolar.name} (${points.length} points)`);
      } else {
        console.warn('⚠️ No active polar table found, creating default');
        await this.createDefaultPolar();
      }
    } catch (error) {
      console.error('Error loading active polar:', error);
      await this.createDefaultPolar();
    }
  }

  async createDefaultPolar(): Promise<void> {
    try {
      // Create a basic polar table for a typical 40ft sailboat
      const defaultPolar = await this.prisma.polarTable.create({
        data: {
          name: 'Default 40ft Cruiser',
          description: 'Default polar table for a typical 40ft cruising sailboat',
          isActive: true,
          entries: {
            create: this.generateDefaultPolarEntries()
          }
        },
        include: { entries: true }
      });

      this.activePolarId = defaultPolar.id;
      const points = defaultPolar.entries.map(entry => ({
        tws: entry.tws,
        twa: entry.twa,
        targetSpeed: entry.targetSpeed
      }));
      this.polarCache.set(defaultPolar.id, points);
      console.log(`📊 Created default polar table: ${defaultPolar.name}`);
    } catch (error) {
      console.error('Error creating default polar:', error);
    }
  }

  private generateDefaultPolarEntries(): Array<{tws: number, twa: number, targetSpeed: number}> {
    const entries: Array<{tws: number, twa: number, targetSpeed: number}> = [];
    
    // Wind speeds from 6 to 25 knots
    const windSpeeds = [6, 8, 10, 12, 14, 16, 20, 25];
    
    // Wind angles from 30 to 180 degrees
    const windAngles = [30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180];

    for (const tws of windSpeeds) {
      for (const twa of windAngles) {
        let targetSpeed: number;

        // Basic polar curve approximation
        if (twa < 45) {
          // Close hauled - speed drops off quickly
          targetSpeed = Math.max(0, tws * 0.3 * (twa / 45));
        } else if (twa < 90) {
          // Close reach - good speed
          targetSpeed = tws * (0.4 + 0.2 * Math.sin((twa - 45) * Math.PI / 45));
        } else if (twa < 120) {
          // Beam reach - maximum speed
          targetSpeed = tws * (0.6 + 0.1 * Math.sin((twa - 90) * Math.PI / 30));
        } else if (twa < 150) {
          // Broad reach - still fast
          targetSpeed = tws * (0.5 + 0.1 * Math.cos((twa - 120) * Math.PI / 30));
        } else {
          // Running - slower but steady
          targetSpeed = tws * 0.4;
        }

        // Apply wind speed scaling
        if (tws < 8) {
          targetSpeed *= 0.8; // Light air penalty
        } else if (tws > 20) {
          targetSpeed *= 0.9; // Heavy air penalty
        }

        // Ensure reasonable bounds
        targetSpeed = Math.max(0.5, Math.min(targetSpeed, tws * 0.8));

        entries.push({
          tws,
          twa,
          targetSpeed: Math.round(targetSpeed * 10) / 10
        });
      }
    }

    return entries;
  }

  async setActivePolar(polarId: string): Promise<void> {
    try {
      // Deactivate current polar
      if (this.activePolarId) {
        await this.prisma.polarTable.update({
          where: { id: this.activePolarId },
          data: { isActive: false }
        });
      }

      // Activate new polar
      const polar = await this.prisma.polarTable.update({
        where: { id: polarId },
        data: { isActive: true },
        include: { entries: true }
      });

      this.activePolarId = polarId;
      const points = polar.entries.map(entry => ({
        tws: entry.tws,
        twa: entry.twa,
        targetSpeed: entry.targetSpeed
      }));
      this.polarCache.set(polarId, points);
      
      console.log(`📊 Switched to polar table: ${polar.name}`);
    } catch (error) {
      console.error('Error setting active polar:', error);
      throw error;
    }
  }

  getTargetSpeed(tws: number, twa: number, seaState: SeaStateType = SeaStateType.CALM): InterpolatedPolar {
    if (!this.activePolarId || !this.polarCache.has(this.activePolarId)) {
      return { targetSpeed: 0, targetVMG: 0 };
    }

    const points = this.polarCache.get(this.activePolarId)!;
    const interpolated = this.interpolatePolar(points, tws, twa);
    
    // Apply sea state correction
    const seaStateMultiplier = this.getSeaStateMultiplier(seaState);
    const correctedSpeed = interpolated.targetSpeed * seaStateMultiplier;
    const targetVMG = correctedSpeed * Math.cos((twa * Math.PI) / 180);

    return {
      targetSpeed: Math.round(correctedSpeed * 10) / 10,
      targetVMG: Math.round(targetVMG * 10) / 10
    };
  }

  private interpolatePolar(points: PolarPoint[], tws: number, twa: number): InterpolatedPolar {
    // Find surrounding wind speeds
    const twsValues = [...new Set(points.map(p => p.tws))].sort((a, b) => a - b);
    const lowerTWS = twsValues.filter(t => t <= tws).pop() || twsValues[0];
    const upperTWS = twsValues.filter(t => t >= tws).shift() || twsValues[twsValues.length - 1];

    if (lowerTWS === upperTWS) {
      // Exact TWS match, interpolate TWA only
      return this.interpolateTWA(points.filter(p => p.tws === lowerTWS), twa);
    }

    // Interpolate between two wind speeds
    const lowerPoints = points.filter(p => p.tws === lowerTWS);
    const upperPoints = points.filter(p => p.tws === upperTWS);

    const lowerResult = this.interpolateTWA(lowerPoints, twa);
    const upperResult = this.interpolateTWA(upperPoints, twa);

    // Linear interpolation between wind speeds
    const twsRatio = (tws - lowerTWS) / (upperTWS - lowerTWS);
    const targetSpeed = lowerResult.targetSpeed + twsRatio * (upperResult.targetSpeed - lowerResult.targetSpeed);
    const targetVMG = targetSpeed * Math.cos((twa * Math.PI) / 180);

    return {
      targetSpeed: Math.round(targetSpeed * 10) / 10,
      targetVMG: Math.round(targetVMG * 10) / 10
    };
  }

  private interpolateTWA(points: PolarPoint[], twa: number): InterpolatedPolar {
    if (points.length === 0) {
      return { targetSpeed: 0, targetVMG: 0 };
    }

    // Sort points by TWA
    const sortedPoints = points.sort((a, b) => a.twa - b.twa);

    // Find surrounding angles
    const lowerPoint = sortedPoints.filter(p => p.twa <= twa).pop();
    const upperPoint = sortedPoints.filter(p => p.twa >= twa).shift();

    if (!lowerPoint && !upperPoint) {
      return { targetSpeed: 0, targetVMG: 0 };
    }

    if (!lowerPoint) {
      const targetSpeed = upperPoint!.targetSpeed;
      return {
        targetSpeed,
        targetVMG: targetSpeed * Math.cos((twa * Math.PI) / 180)
      };
    }

    if (!upperPoint || lowerPoint.twa === upperPoint.twa) {
      const targetSpeed = lowerPoint.targetSpeed;
      return {
        targetSpeed,
        targetVMG: targetSpeed * Math.cos((twa * Math.PI) / 180)
      };
    }

    // Linear interpolation between angles
    const twaRatio = (twa - lowerPoint.twa) / (upperPoint.twa - lowerPoint.twa);
    const targetSpeed = lowerPoint.targetSpeed + twaRatio * (upperPoint.targetSpeed - lowerPoint.targetSpeed);
    const targetVMG = targetSpeed * Math.cos((twa * Math.PI) / 180);

    return {
      targetSpeed: Math.round(targetSpeed * 10) / 10,
      targetVMG: Math.round(targetVMG * 10) / 10
    };
  }

  private getSeaStateMultiplier(seaState: SeaStateType): number {
    switch (seaState) {
      case SeaStateType.CALM:
        return 1.0;
      case SeaStateType.MODERATE:
        return 0.95;
      case SeaStateType.ROUGH:
        return 0.85;
      case SeaStateType.VERY_ROUGH:
        return 0.75;
      default:
        return 1.0;
    }
  }

  async getAllPolars(): Promise<PolarTable[]> {
    const polars = await this.prisma.polarTable.findMany({
      include: { entries: true }
    });

    return polars.map(polar => ({
      name: polar.name,
      description: polar.description || undefined,
      entries: polar.entries.map(entry => ({
        tws: entry.tws,
        twa: entry.twa,
        targetSpeed: entry.targetSpeed,
        sailConfiguration: entry.sailConfiguration || undefined
      }))
    }));
  }

  async createPolar(polar: PolarTable): Promise<string> {
    const created = await this.prisma.polarTable.create({
      data: {
        name: polar.name,
        description: polar.description,
        isActive: false,
        entries: {
          create: polar.entries.map(entry => ({
            tws: entry.tws,
            twa: entry.twa,
            targetSpeed: entry.targetSpeed,
            sailConfiguration: entry.sailConfiguration
          }))
        }
      }
    });

    return created.id;
  }

  async updatePolar(polarId: string, polar: PolarTable): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      // Delete existing entries
      await tx.polarEntry.deleteMany({
        where: { polarTableId: polarId }
      });

      // Update polar table and create new entries
      await tx.polarTable.update({
        where: { id: polarId },
        data: {
          name: polar.name,
          description: polar.description,
          entries: {
            create: polar.entries.map(entry => ({
              tws: entry.tws,
              twa: entry.twa,
              targetSpeed: entry.targetSpeed,
              sailConfiguration: entry.sailConfiguration
            }))
          }
        }
      });
    });

    // Reload cache if this is the active polar
    if (polarId === this.activePolarId) {
      await this.loadActivePolar();
    }
  }

  async deletePolar(polarId: string): Promise<void> {
    if (polarId === this.activePolarId) {
      throw new Error('Cannot delete active polar table');
    }

    await this.prisma.polarTable.delete({
      where: { id: polarId }
    });

    this.polarCache.delete(polarId);
  }

  getActivePolarId(): string | null {
    return this.activePolarId;
  }
}
