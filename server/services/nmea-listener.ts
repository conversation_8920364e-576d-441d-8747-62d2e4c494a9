import { Socket } from 'net';
import { Server as SocketIOServer } from 'socket.io';
import { PerformanceCalculator } from './performance-calculator.js';
import { LiveData, SeaStateType } from '../../types.js';

interface NMEAData {
  bsp?: number;      // Boat Speed (VHW)
  tws?: number;      // True Wind Speed (MWV)
  twa?: number;      // True Wind Angle (MWV)
  aws?: number;      // Apparent Wind Speed (MWV)
  awa?: number;      // Apparent Wind Angle (MWV)
  lat?: number;      // Latitude (GLL)
  lon?: number;      // Longitude (GLL)
  heading?: number;  // True heading
  sog?: number;      // Speed Over Ground
  cog?: number;      // Course Over Ground
  depth?: number;    // Depth
  heel?: number;     // Heel angle (XDR)
  pitch?: number;    // Pitch angle (XDR)
  roll?: number;     // Roll angle (XDR)
  utcTime?: string;  // UTC time
}

export class NMEAListener {
  private socket: Socket | null = null;
  private isConnected = false;
  private reconnectTimer: NodeJS.Timeout | null = null;
  private dataBuffer = '';
  private currentData: NMEAData = {};
  private lastDataUpdate = 0;
  private readonly DATA_TIMEOUT = 5000; // 5 seconds
  private readonly RECONNECT_INTERVAL = 3000; // 3 seconds

  constructor(
    private host: string,
    private port: number,
    private performanceCalculator: PerformanceCalculator,
    private io: SocketIOServer
  ) {}

  start(): void {
    console.log(`🌊 Starting NMEA listener on ${this.host}:${this.port}`);
    this.connect();
  }

  stop(): void {
    console.log('🛑 Stopping NMEA listener');
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }
    this.isConnected = false;
  }

  private connect(): void {
    if (this.socket) {
      this.socket.destroy();
    }

    this.socket = new Socket();
    
    this.socket.on('connect', () => {
      console.log(`✅ Connected to NMEA source at ${this.host}:${this.port}`);
      this.isConnected = true;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
    });

    this.socket.on('data', (data: Buffer) => {
      this.dataBuffer += data.toString();
      this.processBuffer();
    });

    this.socket.on('error', (error: Error) => {
      console.error('❌ NMEA connection error:', error.message);
      this.handleDisconnection();
    });

    this.socket.on('close', () => {
      console.log('🔌 NMEA connection closed');
      this.handleDisconnection();
    });

    this.socket.connect(this.port, this.host);
  }

  private handleDisconnection(): void {
    this.isConnected = false;
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }

    if (!this.reconnectTimer) {
      console.log(`🔄 Attempting to reconnect in ${this.RECONNECT_INTERVAL / 1000}s...`);
      this.reconnectTimer = setTimeout(() => {
        this.reconnectTimer = null;
        this.connect();
      }, this.RECONNECT_INTERVAL);
    }
  }

  private processBuffer(): void {
    const lines = this.dataBuffer.split('\r\n');
    this.dataBuffer = lines.pop() || ''; // Keep incomplete line in buffer

    for (const line of lines) {
      if (line.trim()) {
        this.parseLine(line.trim());
      }
    }
  }

  private parseLine(line: string): void {
    try {
      // NMEA sentences start with $ and have comma-separated fields
      if (!line.startsWith('$')) return;

      const parts = line.split(',');
      const sentenceType = parts[0].substring(3); // Remove $XX prefix

      switch (sentenceType) {
        case 'VHW': // Water speed and heading
          this.parseVHW(parts);
          break;
        case 'MWV': // Wind speed and angle
          this.parseMWV(parts);
          break;
        case 'GLL': // Geographic position
          this.parseGLL(parts);
          break;
        case 'XDR': // Transducer measurement
          this.parseXDR(parts);
          break;
        case 'HDT': // True heading
          this.parseHDT(parts);
          break;
        case 'VTG': // Track made good and ground speed
          this.parseVTG(parts);
          break;
        case 'DBT': // Depth below transducer
          this.parseDBT(parts);
          break;
      }

      this.lastDataUpdate = Date.now();
      this.checkAndEmitData();
    } catch (error) {
      console.error('Error parsing NMEA line:', line, error);
    }
  }

  private parseVHW(parts: string[]): void {
    // $XXVHW,x.x,T,x.x,M,x.x,N,x.x,K*hh
    // Water speed and heading
    if (parts.length >= 8) {
      const speedKnots = parseFloat(parts[5]);
      if (!isNaN(speedKnots)) {
        this.currentData.bsp = speedKnots;
      }
    }
  }

  private parseMWV(parts: string[]): void {
    // $XXMWV,x.x,a,x.x,a,A*hh
    // Wind speed and angle
    if (parts.length >= 6) {
      const angle = parseFloat(parts[1]);
      const reference = parts[2]; // R = Relative, T = True
      const speed = parseFloat(parts[3]);
      const units = parts[4]; // K = km/h, M = m/s, N = knots

      if (!isNaN(angle) && !isNaN(speed)) {
        let speedKnots = speed;
        if (units === 'K') speedKnots = speed * 0.539957; // km/h to knots
        if (units === 'M') speedKnots = speed * 1.94384; // m/s to knots

        if (reference === 'T') {
          this.currentData.tws = speedKnots;
          this.currentData.twa = angle;
        } else if (reference === 'R') {
          this.currentData.aws = speedKnots;
          this.currentData.awa = angle;
        }
      }
    }
  }

  private parseGLL(parts: string[]): void {
    // $XXGLL,llll.ll,a,yyyyy.yy,a,hhmmss.ss,A,a*hh
    // Geographic position
    if (parts.length >= 7) {
      const latStr = parts[1];
      const latDir = parts[2];
      const lonStr = parts[3];
      const lonDir = parts[4];
      const timeStr = parts[5];

      if (latStr && lonStr) {
        const lat = this.parseCoordinate(latStr, latDir);
        const lon = this.parseCoordinate(lonStr, lonDir);
        
        if (lat !== null && lon !== null) {
          this.currentData.lat = lat;
          this.currentData.lon = lon;
        }
      }

      if (timeStr) {
        this.currentData.utcTime = new Date().toISOString();
      }
    }
  }

  private parseXDR(parts: string[]): void {
    // $XXHDT,x.x,T*hh
    // Transducer measurement (heel, pitch, roll)
    if (parts.length >= 5) {
      const type = parts[1];
      const value = parseFloat(parts[2]);
      const instance = parts[4];

      if (!isNaN(value)) {
        if (instance.includes('HEEL') || type === 'A') {
          this.currentData.heel = value;
        } else if (instance.includes('PITCH') || type === 'A') {
          this.currentData.pitch = value;
        } else if (instance.includes('ROLL') || type === 'A') {
          this.currentData.roll = value;
        }
      }
    }
  }

  private parseHDT(parts: string[]): void {
    // $XXHDT,x.x,T*hh
    // True heading
    if (parts.length >= 3) {
      const heading = parseFloat(parts[1]);
      if (!isNaN(heading)) {
        this.currentData.heading = heading;
      }
    }
  }

  private parseVTG(parts: string[]): void {
    // $XXVTG,x.x,T,x.x,M,x.x,N,x.x,K,a*hh
    // Track made good and ground speed
    if (parts.length >= 8) {
      const cogTrue = parseFloat(parts[1]);
      const sogKnots = parseFloat(parts[5]);

      if (!isNaN(cogTrue)) this.currentData.cog = cogTrue;
      if (!isNaN(sogKnots)) this.currentData.sog = sogKnots;
    }
  }

  private parseDBT(parts: string[]): void {
    // $XXDBT,x.x,f,x.x,M,x.x,F*hh
    // Depth below transducer
    if (parts.length >= 4) {
      const depthMeters = parseFloat(parts[3]);
      if (!isNaN(depthMeters)) {
        this.currentData.depth = depthMeters;
      }
    }
  }

  private parseCoordinate(coordStr: string, direction: string): number | null {
    if (!coordStr || !direction) return null;

    const coord = parseFloat(coordStr);
    if (isNaN(coord)) return null;

    // Convert DDMM.MMMM to decimal degrees
    const degrees = Math.floor(coord / 100);
    const minutes = coord % 100;
    let decimal = degrees + minutes / 60;

    // Apply direction
    if (direction === 'S' || direction === 'W') {
      decimal = -decimal;
    }

    return decimal;
  }

  private checkAndEmitData(): void {
    // Only emit if we have essential data
    if (this.currentData.bsp !== undefined && 
        this.currentData.tws !== undefined && 
        this.currentData.twa !== undefined) {
      
      const liveData = this.buildLiveData();
      this.io.emit('liveUpdate', liveData);
    }
  }

  private buildLiveData(): LiveData {
    const vmg = this.currentData.bsp! * Math.cos((this.currentData.twa! * Math.PI) / 180);
    
    // Calculate performance using polar engine
    const performance = this.performanceCalculator.calculatePerformance(
      this.currentData.bsp!,
      this.currentData.tws!,
      this.currentData.twa!,
      this.estimateSeaState()
    );

    return {
      bsp: this.currentData.bsp!,
      tws: this.currentData.tws!,
      twa: this.currentData.twa!,
      vmg,
      seaState: this.estimateSeaState(),
      targetVMG: performance.targetVMG,
      percentPolar: performance.percentPolar,
      tip: performance.tip,
      heel: this.currentData.heel || 0,
      pitch: this.currentData.pitch || 0,
      roll: this.currentData.roll || 0,
      utcTime: new Date().toISOString(),
      lat: this.currentData.lat,
      lon: this.currentData.lon,
      heading: this.currentData.heading,
      sog: this.currentData.sog,
      cog: this.currentData.cog,
      depth: this.currentData.depth,
      aws: this.currentData.aws,
      awa: this.currentData.awa,
    };
  }

  private estimateSeaState(): SeaStateType {
    // Estimate sea state based on heel and pitch motion
    const heel = Math.abs(this.currentData.heel || 0);
    const pitch = Math.abs(this.currentData.pitch || 0);
    
    const motion = heel + pitch;
    
    if (motion < 5) return SeaStateType.CALM;
    if (motion < 15) return SeaStateType.MODERATE;
    if (motion < 25) return SeaStateType.ROUGH;
    return SeaStateType.VERY_ROUGH;
  }

  isListening(): boolean {
    return this.isConnected;
  }

  getLastDataTime(): number {
    return this.lastDataUpdate;
  }
}
