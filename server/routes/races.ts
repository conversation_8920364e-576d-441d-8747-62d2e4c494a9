import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const WaypointSchema = z.object({
  lat: z.number().min(-90).max(90),
  lon: z.number().min(-180).max(180),
  name: z.string().optional()
});

const RaceEventSchema = z.object({
  name: z.string().min(1).max(200),
  startTime: z.string().datetime(),
  endTime: z.string().datetime().optional(),
  notes: z.string().optional(),
  route: z.array(WaypointSchema).optional()
});

export function setupRaceRoutes(
  app: Express,
  prisma: PrismaClient,
  apiBase: string
): void {

  // Get all race events
  app.get(`${apiBase}/races`, async (req: Request, res: Response) => {
    try {
      const races = await prisma.raceEvent.findMany({
        include: {
          rsvps: {
            include: {
              crewMember: true
            }
          },
          _count: {
            select: { 
              rsvps: true,
              performances: true
            }
          }
        },
        orderBy: { startTime: 'desc' }
      });

      const response = races.map(race => ({
        id: race.id,
        name: race.name,
        startTime: race.startTime,
        endTime: race.endTime,
        notes: race.notes,
        route: race.route,
        createdAt: race.createdAt,
        updatedAt: race.updatedAt,
        rsvpCount: race._count.rsvps,
        performanceDataCount: race._count.performances,
        rsvps: race.rsvps.map(rsvp => ({
          id: rsvp.id,
          status: rsvp.status,
          notes: rsvp.notes,
          crewMember: {
            id: rsvp.crewMember.id,
            name: rsvp.crewMember.name,
            role: rsvp.crewMember.role
          }
        }))
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching races:', error);
      res.status(500).json({ error: 'Failed to fetch race events' });
    }
  });

  // Get specific race event
  app.get(`${apiBase}/races/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      
      const race = await prisma.raceEvent.findUnique({
        where: { id },
        include: {
          rsvps: {
            include: {
              crewMember: true
            }
          },
          performances: {
            orderBy: { timestamp: 'asc' },
            take: 1000 // Limit to last 1000 data points
          }
        }
      });

      if (!race) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      const response = {
        id: race.id,
        name: race.name,
        startTime: race.startTime,
        endTime: race.endTime,
        notes: race.notes,
        route: race.route,
        createdAt: race.createdAt,
        updatedAt: race.updatedAt,
        rsvps: race.rsvps.map(rsvp => ({
          id: rsvp.id,
          status: rsvp.status,
          notes: rsvp.notes,
          crewMember: {
            id: rsvp.crewMember.id,
            name: rsvp.crewMember.name,
            role: rsvp.crewMember.role
          }
        })),
        performances: race.performances.map(perf => ({
          id: perf.id,
          timestamp: perf.timestamp,
          bsp: perf.bsp,
          tws: perf.tws,
          twa: perf.twa,
          vmg: perf.vmg,
          targetVMG: perf.targetVMG,
          percentPolar: perf.percentPolar,
          seaState: perf.seaState,
          tip: perf.tip,
          lat: perf.lat,
          lon: perf.lon,
          heading: perf.heading,
          sog: perf.sog,
          cog: perf.cog
        }))
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching race:', error);
      res.status(500).json({ error: 'Failed to fetch race event' });
    }
  });

  // Create new race event
  app.post(`${apiBase}/races`, async (req: Request, res: Response) => {
    try {
      const validation = RaceEventSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid race event data',
          details: validation.error.errors
        });
      }

      const { name, startTime, endTime, notes, route } = validation.data;

      const race = await prisma.raceEvent.create({
        data: {
          name,
          startTime: new Date(startTime),
          endTime: endTime ? new Date(endTime) : null,
          notes,
          route: route || null
        }
      });

      res.status(201).json({
        id: race.id,
        name: race.name,
        startTime: race.startTime,
        endTime: race.endTime,
        notes: race.notes,
        route: race.route,
        createdAt: race.createdAt
      });
    } catch (error) {
      console.error('Error creating race:', error);
      res.status(500).json({ error: 'Failed to create race event' });
    }
  });

  // Update race event
  app.put(`${apiBase}/races/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const validation = RaceEventSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid race event data',
          details: validation.error.errors
        });
      }

      const { name, startTime, endTime, notes, route } = validation.data;

      // Check if race exists
      const existing = await prisma.raceEvent.findUnique({
        where: { id }
      });

      if (!existing) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      const race = await prisma.raceEvent.update({
        where: { id },
        data: {
          name,
          startTime: new Date(startTime),
          endTime: endTime ? new Date(endTime) : null,
          notes,
          route: route || null
        }
      });

      res.json({
        id: race.id,
        name: race.name,
        startTime: race.startTime,
        endTime: race.endTime,
        notes: race.notes,
        route: race.route,
        updatedAt: race.updatedAt
      });
    } catch (error) {
      console.error('Error updating race:', error);
      res.status(500).json({ error: 'Failed to update race event' });
    }
  });

  // Delete race event
  app.delete(`${apiBase}/races/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if race exists
      const race = await prisma.raceEvent.findUnique({
        where: { id }
      });

      if (!race) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      await prisma.raceEvent.delete({
        where: { id }
      });

      res.json({ message: 'Race event deleted successfully' });
    } catch (error) {
      console.error('Error deleting race:', error);
      res.status(500).json({ error: 'Failed to delete race event' });
    }
  });

  // Get upcoming races
  app.get(`${apiBase}/races/upcoming`, async (req: Request, res: Response) => {
    try {
      const now = new Date();
      const races = await prisma.raceEvent.findMany({
        where: {
          startTime: {
            gte: now
          }
        },
        include: {
          rsvps: {
            include: {
              crewMember: true
            }
          },
          _count: {
            select: { rsvps: true }
          }
        },
        orderBy: { startTime: 'asc' },
        take: 10
      });

      const response = races.map(race => ({
        id: race.id,
        name: race.name,
        startTime: race.startTime,
        endTime: race.endTime,
        notes: race.notes,
        route: race.route,
        rsvpCount: race._count.rsvps,
        rsvps: race.rsvps.map(rsvp => ({
          id: rsvp.id,
          status: rsvp.status,
          crewMember: {
            id: rsvp.crewMember.id,
            name: rsvp.crewMember.name,
            role: rsvp.crewMember.role
          }
        }))
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching upcoming races:', error);
      res.status(500).json({ error: 'Failed to fetch upcoming races' });
    }
  });

  // Start race (set current time as start time)
  app.post(`${apiBase}/races/:id/start`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const race = await prisma.raceEvent.findUnique({
        where: { id }
      });

      if (!race) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      const updatedRace = await prisma.raceEvent.update({
        where: { id },
        data: {
          startTime: new Date()
        }
      });

      res.json({
        message: 'Race started',
        race: {
          id: updatedRace.id,
          name: updatedRace.name,
          startTime: updatedRace.startTime
        }
      });
    } catch (error) {
      console.error('Error starting race:', error);
      res.status(500).json({ error: 'Failed to start race' });
    }
  });

  // Finish race (set current time as end time)
  app.post(`${apiBase}/races/:id/finish`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      const race = await prisma.raceEvent.findUnique({
        where: { id }
      });

      if (!race) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      const updatedRace = await prisma.raceEvent.update({
        where: { id },
        data: {
          endTime: new Date()
        }
      });

      res.json({
        message: 'Race finished',
        race: {
          id: updatedRace.id,
          name: updatedRace.name,
          startTime: updatedRace.startTime,
          endTime: updatedRace.endTime
        }
      });
    } catch (error) {
      console.error('Error finishing race:', error);
      res.status(500).json({ error: 'Failed to finish race' });
    }
  });
}
