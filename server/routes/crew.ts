import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const CrewMemberSchema = z.object({
  name: z.string().min(1).max(100),
  role: z.string().optional(),
  email: z.string().email().optional(),
  phone: z.string().optional()
});

const CrewRSVPSchema = z.object({
  raceId: z.string(),
  crewMemberId: z.string(),
  status: z.enum(['Attending', 'Not Attending', 'Maybe']),
  notes: z.string().optional()
});

export function setupCrewRoutes(
  app: Express,
  prisma: PrismaClient,
  apiBase: string
): void {

  // Get all crew members
  app.get(`${apiBase}/crew`, async (req: Request, res: Response) => {
    try {
      const crew = await prisma.crewMember.findMany({
        include: {
          rsvps: {
            include: {
              race: true
            }
          },
          _count: {
            select: { rsvps: true }
          }
        },
        orderBy: { name: 'asc' }
      });

      const response = crew.map(member => ({
        id: member.id,
        name: member.name,
        role: member.role,
        email: member.email,
        phone: member.phone,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        rsvpCount: member._count.rsvps,
        recentRsvps: member.rsvps
          .sort((a, b) => new Date(b.race.startTime).getTime() - new Date(a.race.startTime).getTime())
          .slice(0, 5)
          .map(rsvp => ({
            id: rsvp.id,
            status: rsvp.status,
            notes: rsvp.notes,
            race: {
              id: rsvp.race.id,
              name: rsvp.race.name,
              startTime: rsvp.race.startTime
            }
          }))
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching crew:', error);
      res.status(500).json({ error: 'Failed to fetch crew members' });
    }
  });

  // Get specific crew member
  app.get(`${apiBase}/crew/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      
      const member = await prisma.crewMember.findUnique({
        where: { id },
        include: {
          rsvps: {
            include: {
              race: true
            },
            orderBy: {
              race: {
                startTime: 'desc'
              }
            }
          }
        }
      });

      if (!member) {
        return res.status(404).json({ error: 'Crew member not found' });
      }

      const response = {
        id: member.id,
        name: member.name,
        role: member.role,
        email: member.email,
        phone: member.phone,
        createdAt: member.createdAt,
        updatedAt: member.updatedAt,
        rsvps: member.rsvps.map(rsvp => ({
          id: rsvp.id,
          status: rsvp.status,
          notes: rsvp.notes,
          createdAt: rsvp.createdAt,
          race: {
            id: rsvp.race.id,
            name: rsvp.race.name,
            startTime: rsvp.race.startTime,
            endTime: rsvp.race.endTime
          }
        }))
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching crew member:', error);
      res.status(500).json({ error: 'Failed to fetch crew member' });
    }
  });

  // Create new crew member
  app.post(`${apiBase}/crew`, async (req: Request, res: Response) => {
    try {
      const validation = CrewMemberSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid crew member data',
          details: validation.error.errors
        });
      }

      const { name, role, email, phone } = validation.data;

      // Check for duplicate email if provided
      if (email) {
        const existing = await prisma.crewMember.findUnique({
          where: { email }
        });

        if (existing) {
          return res.status(409).json({ error: 'Crew member with this email already exists' });
        }
      }

      const member = await prisma.crewMember.create({
        data: {
          name,
          role,
          email,
          phone
        }
      });

      res.status(201).json({
        id: member.id,
        name: member.name,
        role: member.role,
        email: member.email,
        phone: member.phone,
        createdAt: member.createdAt
      });
    } catch (error) {
      console.error('Error creating crew member:', error);
      res.status(500).json({ error: 'Failed to create crew member' });
    }
  });

  // Update crew member
  app.put(`${apiBase}/crew/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const validation = CrewMemberSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid crew member data',
          details: validation.error.errors
        });
      }

      const { name, role, email, phone } = validation.data;

      // Check if crew member exists
      const existing = await prisma.crewMember.findUnique({
        where: { id }
      });

      if (!existing) {
        return res.status(404).json({ error: 'Crew member not found' });
      }

      // Check for duplicate email (excluding current member)
      if (email) {
        const emailConflict = await prisma.crewMember.findFirst({
          where: { 
            email,
            id: { not: id }
          }
        });

        if (emailConflict) {
          return res.status(409).json({ error: 'Another crew member with this email already exists' });
        }
      }

      const member = await prisma.crewMember.update({
        where: { id },
        data: {
          name,
          role,
          email,
          phone
        }
      });

      res.json({
        id: member.id,
        name: member.name,
        role: member.role,
        email: member.email,
        phone: member.phone,
        updatedAt: member.updatedAt
      });
    } catch (error) {
      console.error('Error updating crew member:', error);
      res.status(500).json({ error: 'Failed to update crew member' });
    }
  });

  // Delete crew member
  app.delete(`${apiBase}/crew/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if crew member exists
      const member = await prisma.crewMember.findUnique({
        where: { id }
      });

      if (!member) {
        return res.status(404).json({ error: 'Crew member not found' });
      }

      await prisma.crewMember.delete({
        where: { id }
      });

      res.json({ message: 'Crew member deleted successfully' });
    } catch (error) {
      console.error('Error deleting crew member:', error);
      res.status(500).json({ error: 'Failed to delete crew member' });
    }
  });

  // Get RSVPs for a specific race
  app.get(`${apiBase}/races/:raceId/rsvps`, async (req: Request, res: Response) => {
    try {
      const { raceId } = req.params;

      const rsvps = await prisma.crewRSVP.findMany({
        where: { raceId },
        include: {
          crewMember: true,
          race: true
        },
        orderBy: {
          crewMember: {
            name: 'asc'
          }
        }
      });

      const response = rsvps.map(rsvp => ({
        id: rsvp.id,
        status: rsvp.status,
        notes: rsvp.notes,
        createdAt: rsvp.createdAt,
        updatedAt: rsvp.updatedAt,
        crewMember: {
          id: rsvp.crewMember.id,
          name: rsvp.crewMember.name,
          role: rsvp.crewMember.role,
          email: rsvp.crewMember.email
        },
        race: {
          id: rsvp.race.id,
          name: rsvp.race.name,
          startTime: rsvp.race.startTime
        }
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching RSVPs:', error);
      res.status(500).json({ error: 'Failed to fetch RSVPs' });
    }
  });

  // Create or update RSVP
  app.post(`${apiBase}/rsvps`, async (req: Request, res: Response) => {
    try {
      const validation = CrewRSVPSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid RSVP data',
          details: validation.error.errors
        });
      }

      const { raceId, crewMemberId, status, notes } = validation.data;

      // Check if race and crew member exist
      const [race, crewMember] = await Promise.all([
        prisma.raceEvent.findUnique({ where: { id: raceId } }),
        prisma.crewMember.findUnique({ where: { id: crewMemberId } })
      ]);

      if (!race) {
        return res.status(404).json({ error: 'Race event not found' });
      }

      if (!crewMember) {
        return res.status(404).json({ error: 'Crew member not found' });
      }

      // Upsert RSVP
      const rsvp = await prisma.crewRSVP.upsert({
        where: {
          raceId_crewMemberId: {
            raceId,
            crewMemberId
          }
        },
        update: {
          status,
          notes
        },
        create: {
          raceId,
          crewMemberId,
          status,
          notes
        },
        include: {
          crewMember: true,
          race: true
        }
      });

      res.json({
        id: rsvp.id,
        status: rsvp.status,
        notes: rsvp.notes,
        createdAt: rsvp.createdAt,
        updatedAt: rsvp.updatedAt,
        crewMember: {
          id: rsvp.crewMember.id,
          name: rsvp.crewMember.name,
          role: rsvp.crewMember.role
        },
        race: {
          id: rsvp.race.id,
          name: rsvp.race.name,
          startTime: rsvp.race.startTime
        }
      });
    } catch (error) {
      console.error('Error creating/updating RSVP:', error);
      res.status(500).json({ error: 'Failed to create/update RSVP' });
    }
  });

  // Delete RSVP
  app.delete(`${apiBase}/rsvps/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if RSVP exists
      const rsvp = await prisma.crewRSVP.findUnique({
        where: { id }
      });

      if (!rsvp) {
        return res.status(404).json({ error: 'RSVP not found' });
      }

      await prisma.crewRSVP.delete({
        where: { id }
      });

      res.json({ message: 'RSVP deleted successfully' });
    } catch (error) {
      console.error('Error deleting RSVP:', error);
      res.status(500).json({ error: 'Failed to delete RSVP' });
    }
  });
}
