import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const SettingSchema = z.object({
  key: z.string().min(1).max(100),
  value: z.any()
});

export function setupSettingsRoutes(
  app: Express,
  prisma: PrismaClient,
  apiBase: string
): void {

  // Get all settings
  app.get(`${apiBase}/settings`, async (req: Request, res: Response) => {
    try {
      const settings = await prisma.settings.findMany({
        orderBy: { key: 'asc' }
      });

      const response = settings.reduce((acc, setting) => {
        acc[setting.key] = setting.value;
        return acc;
      }, {} as Record<string, any>);

      res.json(response);
    } catch (error) {
      console.error('Error fetching settings:', error);
      res.status(500).json({ error: 'Failed to fetch settings' });
    }
  });

  // Get specific setting
  app.get(`${apiBase}/settings/:key`, async (req: Request, res: Response) => {
    try {
      const { key } = req.params;
      
      const setting = await prisma.settings.findUnique({
        where: { key }
      });

      if (!setting) {
        return res.status(404).json({ error: 'Setting not found' });
      }

      res.json({
        key: setting.key,
        value: setting.value,
        updatedAt: setting.updatedAt
      });
    } catch (error) {
      console.error('Error fetching setting:', error);
      res.status(500).json({ error: 'Failed to fetch setting' });
    }
  });

  // Update or create setting
  app.put(`${apiBase}/settings/:key`, async (req: Request, res: Response) => {
    try {
      const { key } = req.params;
      const { value } = req.body;

      if (value === undefined) {
        return res.status(400).json({ error: 'Value is required' });
      }

      const setting = await prisma.settings.upsert({
        where: { key },
        update: { value },
        create: { key, value }
      });

      res.json({
        key: setting.key,
        value: setting.value,
        updatedAt: setting.updatedAt,
        message: 'Setting updated successfully'
      });
    } catch (error) {
      console.error('Error updating setting:', error);
      res.status(500).json({ error: 'Failed to update setting' });
    }
  });

  // Update multiple settings
  app.put(`${apiBase}/settings`, async (req: Request, res: Response) => {
    try {
      const settings = req.body;

      if (!settings || typeof settings !== 'object') {
        return res.status(400).json({ error: 'Settings object is required' });
      }

      const updates = Object.entries(settings).map(([key, value]) => 
        prisma.settings.upsert({
          where: { key },
          update: { value },
          create: { key, value }
        })
      );

      await Promise.all(updates);

      res.json({
        message: 'Settings updated successfully',
        updatedKeys: Object.keys(settings)
      });
    } catch (error) {
      console.error('Error updating settings:', error);
      res.status(500).json({ error: 'Failed to update settings' });
    }
  });

  // Delete setting
  app.delete(`${apiBase}/settings/:key`, async (req: Request, res: Response) => {
    try {
      const { key } = req.params;

      const setting = await prisma.settings.findUnique({
        where: { key }
      });

      if (!setting) {
        return res.status(404).json({ error: 'Setting not found' });
      }

      await prisma.settings.delete({
        where: { key }
      });

      res.json({ message: 'Setting deleted successfully' });
    } catch (error) {
      console.error('Error deleting setting:', error);
      res.status(500).json({ error: 'Failed to delete setting' });
    }
  });

  // Get default settings
  app.get(`${apiBase}/settings/defaults`, async (req: Request, res: Response) => {
    try {
      const defaults = {
        // NMEA Connection Settings
        'nmea.host': 'localhost',
        'nmea.port': 2000,
        'nmea.autoReconnect': true,
        'nmea.reconnectInterval': 3000,
        
        // Data Smoothing Settings
        'smoothing.windowSize': 10,
        'smoothing.windowTimeMs': 30000,
        'smoothing.enabled': true,
        
        // Performance Settings
        'performance.autoStore': true,
        'performance.storeInterval': 5000,
        'performance.retentionDays': 30,
        
        // Display Settings
        'display.units': 'metric', // metric or imperial
        'display.theme': 'dark',
        'display.updateInterval': 2000,
        'display.showDebugInfo': false,
        
        // Alerts Settings
        'alerts.enabled': true,
        'alerts.performanceThreshold': 80,
        'alerts.windShiftThreshold': 10,
        'alerts.soundEnabled': false,
        
        // Race Settings
        'race.autoStart': false,
        'race.startSequenceDuration': 300, // 5 minutes
        'race.recordPerformance': true,
        
        // Polar Settings
        'polar.seaStateCompensation': true,
        'polar.autoUpdate': false,
        'polar.interpolationMethod': 'linear',
        
        // Export Settings
        'export.format': 'csv',
        'export.includeRawData': false,
        'export.timezone': 'UTC'
      };

      res.json(defaults);
    } catch (error) {
      console.error('Error fetching default settings:', error);
      res.status(500).json({ error: 'Failed to fetch default settings' });
    }
  });

  // Reset settings to defaults
  app.post(`${apiBase}/settings/reset`, async (req: Request, res: Response) => {
    try {
      const { keys } = req.body; // Optional array of specific keys to reset

      if (keys && Array.isArray(keys)) {
        // Reset specific settings
        await prisma.settings.deleteMany({
          where: {
            key: {
              in: keys
            }
          }
        });

        res.json({
          message: 'Specified settings reset to defaults',
          resetKeys: keys
        });
      } else {
        // Reset all settings
        await prisma.settings.deleteMany({});

        res.json({
          message: 'All settings reset to defaults'
        });
      }
    } catch (error) {
      console.error('Error resetting settings:', error);
      res.status(500).json({ error: 'Failed to reset settings' });
    }
  });

  // Get system information
  app.get(`${apiBase}/settings/system`, async (req: Request, res: Response) => {
    try {
      const systemInfo = {
        version: process.env.npm_package_version || '1.0.0',
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV || 'development',
        databaseUrl: process.env.DATABASE_URL ? 'configured' : 'not configured',
        nmeaHost: process.env.NMEA_HOST || 'localhost',
        nmeaPort: process.env.NMEA_PORT || '2000'
      };

      res.json(systemInfo);
    } catch (error) {
      console.error('Error fetching system info:', error);
      res.status(500).json({ error: 'Failed to fetch system information' });
    }
  });

  // Validate settings
  app.post(`${apiBase}/settings/validate`, async (req: Request, res: Response) => {
    try {
      const settings = req.body;
      const errors: string[] = [];

      // Validate NMEA settings
      if (settings['nmea.port'] && (isNaN(settings['nmea.port']) || settings['nmea.port'] < 1 || settings['nmea.port'] > 65535)) {
        errors.push('NMEA port must be a valid port number (1-65535)');
      }

      if (settings['nmea.reconnectInterval'] && (isNaN(settings['nmea.reconnectInterval']) || settings['nmea.reconnectInterval'] < 1000)) {
        errors.push('NMEA reconnect interval must be at least 1000ms');
      }

      // Validate smoothing settings
      if (settings['smoothing.windowSize'] && (isNaN(settings['smoothing.windowSize']) || settings['smoothing.windowSize'] < 1 || settings['smoothing.windowSize'] > 100)) {
        errors.push('Smoothing window size must be between 1 and 100');
      }

      if (settings['smoothing.windowTimeMs'] && (isNaN(settings['smoothing.windowTimeMs']) || settings['smoothing.windowTimeMs'] < 1000)) {
        errors.push('Smoothing window time must be at least 1000ms');
      }

      // Validate performance settings
      if (settings['performance.storeInterval'] && (isNaN(settings['performance.storeInterval']) || settings['performance.storeInterval'] < 1000)) {
        errors.push('Performance store interval must be at least 1000ms');
      }

      if (settings['performance.retentionDays'] && (isNaN(settings['performance.retentionDays']) || settings['performance.retentionDays'] < 1)) {
        errors.push('Performance retention days must be at least 1');
      }

      // Validate display settings
      if (settings['display.units'] && !['metric', 'imperial'].includes(settings['display.units'])) {
        errors.push('Display units must be either "metric" or "imperial"');
      }

      if (settings['display.updateInterval'] && (isNaN(settings['display.updateInterval']) || settings['display.updateInterval'] < 500)) {
        errors.push('Display update interval must be at least 500ms');
      }

      // Validate alert settings
      if (settings['alerts.performanceThreshold'] && (isNaN(settings['alerts.performanceThreshold']) || settings['alerts.performanceThreshold'] < 0 || settings['alerts.performanceThreshold'] > 100)) {
        errors.push('Performance threshold must be between 0 and 100');
      }

      if (errors.length > 0) {
        return res.status(400).json({
          valid: false,
          errors
        });
      }

      res.json({
        valid: true,
        message: 'All settings are valid'
      });
    } catch (error) {
      console.error('Error validating settings:', error);
      res.status(500).json({ error: 'Failed to validate settings' });
    }
  });
}
