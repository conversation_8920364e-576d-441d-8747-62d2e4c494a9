import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';

// Validation schemas
const SailPlanSchema = z.object({
  headsail: z.string().min(1).max(50),
  spinnaker: z.string().min(1).max(50),
  reef: z.string().min(1).max(50)
});

export function setupSailPlanRoutes(
  app: Express,
  prisma: PrismaClient,
  apiBase: string
): void {

  // Get current active sail plan
  app.get(`${apiBase}/sailplan`, async (req: Request, res: Response) => {
    try {
      const activeSailPlan = await prisma.sailPlan.findFirst({
        where: { isActive: true },
        orderBy: { updatedAt: 'desc' }
      });

      if (!activeSailPlan) {
        // Create default sail plan if none exists
        const defaultPlan = await prisma.sailPlan.create({
          data: {
            headsail: 'J1 (Light)',
            spinnaker: 'None',
            reef: 'None',
            isActive: true
          }
        });

        return res.json({
          id: defaultPlan.id,
          headsail: defaultPlan.headsail,
          spinnaker: defaultPlan.spinnaker,
          reef: defaultPlan.reef,
          isActive: defaultPlan.isActive,
          createdAt: defaultPlan.createdAt,
          updatedAt: defaultPlan.updatedAt
        });
      }

      res.json({
        id: activeSailPlan.id,
        headsail: activeSailPlan.headsail,
        spinnaker: activeSailPlan.spinnaker,
        reef: activeSailPlan.reef,
        isActive: activeSailPlan.isActive,
        createdAt: activeSailPlan.createdAt,
        updatedAt: activeSailPlan.updatedAt
      });
    } catch (error) {
      console.error('Error fetching sail plan:', error);
      res.status(500).json({ error: 'Failed to fetch current sail plan' });
    }
  });

  // Get all sail plans
  app.get(`${apiBase}/sailplan/all`, async (req: Request, res: Response) => {
    try {
      const sailPlans = await prisma.sailPlan.findMany({
        orderBy: { updatedAt: 'desc' }
      });

      const response = sailPlans.map(plan => ({
        id: plan.id,
        headsail: plan.headsail,
        spinnaker: plan.spinnaker,
        reef: plan.reef,
        isActive: plan.isActive,
        createdAt: plan.createdAt,
        updatedAt: plan.updatedAt
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching sail plans:', error);
      res.status(500).json({ error: 'Failed to fetch sail plans' });
    }
  });

  // Update current sail plan
  app.put(`${apiBase}/sailplan`, async (req: Request, res: Response) => {
    try {
      const validation = SailPlanSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid sail plan data',
          details: validation.error.errors
        });
      }

      const { headsail, spinnaker, reef } = validation.data;

      // Use transaction to ensure only one active sail plan
      const updatedPlan = await prisma.$transaction(async (tx) => {
        // Deactivate all existing sail plans
        await tx.sailPlan.updateMany({
          where: { isActive: true },
          data: { isActive: false }
        });

        // Create new active sail plan
        return await tx.sailPlan.create({
          data: {
            headsail,
            spinnaker,
            reef,
            isActive: true
          }
        });
      });

      res.json({
        id: updatedPlan.id,
        headsail: updatedPlan.headsail,
        spinnaker: updatedPlan.spinnaker,
        reef: updatedPlan.reef,
        isActive: updatedPlan.isActive,
        createdAt: updatedPlan.createdAt,
        updatedAt: updatedPlan.updatedAt,
        message: 'Sail plan updated successfully'
      });
    } catch (error) {
      console.error('Error updating sail plan:', error);
      res.status(500).json({ error: 'Failed to update sail plan' });
    }
  });

  // Create new sail plan
  app.post(`${apiBase}/sailplan`, async (req: Request, res: Response) => {
    try {
      const validation = SailPlanSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid sail plan data',
          details: validation.error.errors
        });
      }

      const { headsail, spinnaker, reef } = validation.data;
      const makeActive = req.body.makeActive === true;

      let sailPlan;

      if (makeActive) {
        // Use transaction to ensure only one active sail plan
        sailPlan = await prisma.$transaction(async (tx) => {
          // Deactivate all existing sail plans
          await tx.sailPlan.updateMany({
            where: { isActive: true },
            data: { isActive: false }
          });

          // Create new active sail plan
          return await tx.sailPlan.create({
            data: {
              headsail,
              spinnaker,
              reef,
              isActive: true
            }
          });
        });
      } else {
        // Create inactive sail plan
        sailPlan = await prisma.sailPlan.create({
          data: {
            headsail,
            spinnaker,
            reef,
            isActive: false
          }
        });
      }

      res.status(201).json({
        id: sailPlan.id,
        headsail: sailPlan.headsail,
        spinnaker: sailPlan.spinnaker,
        reef: sailPlan.reef,
        isActive: sailPlan.isActive,
        createdAt: sailPlan.createdAt,
        updatedAt: sailPlan.updatedAt
      });
    } catch (error) {
      console.error('Error creating sail plan:', error);
      res.status(500).json({ error: 'Failed to create sail plan' });
    }
  });

  // Activate specific sail plan
  app.post(`${apiBase}/sailplan/:id/activate`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if sail plan exists
      const sailPlan = await prisma.sailPlan.findUnique({
        where: { id }
      });

      if (!sailPlan) {
        return res.status(404).json({ error: 'Sail plan not found' });
      }

      // Use transaction to ensure only one active sail plan
      const activatedPlan = await prisma.$transaction(async (tx) => {
        // Deactivate all existing sail plans
        await tx.sailPlan.updateMany({
          where: { isActive: true },
          data: { isActive: false }
        });

        // Activate the specified sail plan
        return await tx.sailPlan.update({
          where: { id },
          data: { isActive: true }
        });
      });

      res.json({
        id: activatedPlan.id,
        headsail: activatedPlan.headsail,
        spinnaker: activatedPlan.spinnaker,
        reef: activatedPlan.reef,
        isActive: activatedPlan.isActive,
        updatedAt: activatedPlan.updatedAt,
        message: 'Sail plan activated successfully'
      });
    } catch (error) {
      console.error('Error activating sail plan:', error);
      res.status(500).json({ error: 'Failed to activate sail plan' });
    }
  });

  // Delete sail plan
  app.delete(`${apiBase}/sailplan/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if sail plan exists
      const sailPlan = await prisma.sailPlan.findUnique({
        where: { id }
      });

      if (!sailPlan) {
        return res.status(404).json({ error: 'Sail plan not found' });
      }

      // Prevent deletion of active sail plan
      if (sailPlan.isActive) {
        return res.status(400).json({ error: 'Cannot delete active sail plan' });
      }

      await prisma.sailPlan.delete({
        where: { id }
      });

      res.json({ message: 'Sail plan deleted successfully' });
    } catch (error) {
      console.error('Error deleting sail plan:', error);
      res.status(500).json({ error: 'Failed to delete sail plan' });
    }
  });

  // Get sail plan options/presets
  app.get(`${apiBase}/sailplan/options`, async (req: Request, res: Response) => {
    try {
      // These could be stored in database or configuration
      const options = {
        headsails: [
          'J1 (Light)',
          'J2 (Medium)',
          'J3 (Heavy)',
          'Storm Jib',
          'Genoa',
          'Code 0',
          'Jib Top'
        ],
        spinnakers: [
          'None',
          'A1 (Light VMG)',
          'A2 (Light Runner)',
          'A3 (Reacher)',
          'S2 (Symmetric Runner)',
          'S4 (Symmetric Heavy)',
          'Code 1',
          'Code 2',
          'Code 3'
        ],
        reefs: [
          'None',
          'Reef 1',
          'Reef 2',
          'Reef 3 (Storm)',
          'Deep Reef'
        ]
      };

      res.json(options);
    } catch (error) {
      console.error('Error fetching sail plan options:', error);
      res.status(500).json({ error: 'Failed to fetch sail plan options' });
    }
  });

  // Get sail plan recommendations based on conditions
  app.get(`${apiBase}/sailplan/recommend`, async (req: Request, res: Response) => {
    try {
      const tws = parseFloat(req.query.tws as string);
      const twa = parseFloat(req.query.twa as string);
      const seaState = req.query.seaState as string;

      if (isNaN(tws) || isNaN(twa)) {
        return res.status(400).json({ error: 'Invalid tws or twa parameters' });
      }

      // Basic sail plan recommendations
      let headsail = 'J2 (Medium)';
      let spinnaker = 'None';
      let reef = 'None';

      // Wind strength recommendations
      if (tws < 8) {
        headsail = 'J1 (Light)';
        if (twa > 90) spinnaker = 'A1 (Light VMG)';
      } else if (tws < 15) {
        headsail = 'J2 (Medium)';
        if (twa > 90) spinnaker = 'A2 (Light Runner)';
      } else if (tws < 25) {
        headsail = 'J3 (Heavy)';
        if (twa > 90) spinnaker = 'S2 (Symmetric Runner)';
        if (tws > 20) reef = 'Reef 1';
      } else {
        headsail = 'Storm Jib';
        reef = 'Reef 2';
      }

      // Sea state adjustments
      if (seaState === 'Rough' || seaState === 'Very Rough') {
        if (tws > 15) {
          reef = reef === 'None' ? 'Reef 1' : 'Reef 2';
        }
        if (spinnaker !== 'None' && tws > 18) {
          spinnaker = 'None'; // Drop spinnaker in rough conditions
        }
      }

      const recommendation = {
        conditions: { tws, twa, seaState },
        recommended: { headsail, spinnaker, reef },
        reasoning: this.getSailPlanReasoning(tws, twa, seaState, headsail, spinnaker, reef)
      };

      res.json(recommendation);
    } catch (error) {
      console.error('Error generating sail plan recommendation:', error);
      res.status(500).json({ error: 'Failed to generate sail plan recommendation' });
    }
  });

  // Helper function for sail plan reasoning
  function getSailPlanReasoning(tws: number, twa: number, seaState: string, headsail: string, spinnaker: string, reef: string): string {
    const reasons = [];

    if (tws < 8) {
      reasons.push('Light air conditions favor larger, lighter sails');
    } else if (tws > 20) {
      reasons.push('Strong winds require reduced sail area');
    }

    if (twa > 90 && spinnaker !== 'None') {
      reasons.push('Downwind angle suitable for spinnaker');
    }

    if (reef !== 'None') {
      reasons.push('Reefing recommended for safety and control');
    }

    if (seaState === 'Rough' || seaState === 'Very Rough') {
      reasons.push('Rough sea state requires conservative sail plan');
    }

    return reasons.join('. ') || 'Standard sail configuration for current conditions';
  }
}
