import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { PerformanceCalculator } from '../services/performance-calculator.js';

// Validation schemas
const PerformanceDataSchema = z.object({
  bsp: z.number().min(0).max(50),
  tws: z.number().min(0).max(100),
  twa: z.number().min(0).max(180),
  vmg: z.number(),
  targetVMG: z.number(),
  percentPolar: z.number().min(0).max(200),
  seaState: z.string(),
  tip: z.string().optional(),
  heel: z.number().optional(),
  pitch: z.number().optional(),
  roll: z.number().optional(),
  lat: z.number().optional(),
  lon: z.number().optional(),
  heading: z.number().optional(),
  sog: z.number().optional(),
  cog: z.number().optional(),
  depth: z.number().optional(),
  aws: z.number().optional(),
  awa: z.number().optional(),
  raceId: z.string().optional()
});

export function setupPerformanceRoutes(
  app: Express,
  prisma: PrismaClient,
  performanceCalculator: PerformanceCalculator,
  apiBase: string
): void {

  // Get performance data for a specific time range
  app.get(`${apiBase}/performance`, async (req: Request, res: Response) => {
    try {
      const startTime = req.query.startTime ? new Date(req.query.startTime as string) : new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = req.query.endTime ? new Date(req.query.endTime as string) : new Date();
      const raceId = req.query.raceId as string;
      const limit = parseInt(req.query.limit as string) || 1000;

      const whereClause: any = {
        timestamp: {
          gte: startTime,
          lte: endTime
        }
      };

      if (raceId) {
        whereClause.raceId = raceId;
      }

      const performances = await prisma.performanceData.findMany({
        where: whereClause,
        orderBy: { timestamp: 'desc' },
        take: limit
      });

      const response = performances.map(perf => ({
        id: perf.id,
        timestamp: perf.timestamp,
        bsp: perf.bsp,
        tws: perf.tws,
        twa: perf.twa,
        vmg: perf.vmg,
        targetVMG: perf.targetVMG,
        percentPolar: perf.percentPolar,
        seaState: perf.seaState,
        tip: perf.tip,
        heel: perf.heel,
        pitch: perf.pitch,
        roll: perf.roll,
        lat: perf.lat,
        lon: perf.lon,
        heading: perf.heading,
        sog: perf.sog,
        cog: perf.cog,
        depth: perf.depth,
        aws: perf.aws,
        awa: perf.awa,
        raceId: perf.raceId
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching performance data:', error);
      res.status(500).json({ error: 'Failed to fetch performance data' });
    }
  });

  // Store performance data point
  app.post(`${apiBase}/performance`, async (req: Request, res: Response) => {
    try {
      const validation = PerformanceDataSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid performance data',
          details: validation.error.errors
        });
      }

      const data = validation.data;

      const performance = await prisma.performanceData.create({
        data: {
          bsp: data.bsp,
          tws: data.tws,
          twa: data.twa,
          vmg: data.vmg,
          targetVMG: data.targetVMG,
          percentPolar: data.percentPolar,
          seaState: data.seaState,
          tip: data.tip,
          heel: data.heel,
          pitch: data.pitch,
          roll: data.roll,
          lat: data.lat,
          lon: data.lon,
          heading: data.heading,
          sog: data.sog,
          cog: data.cog,
          depth: data.depth,
          aws: data.aws,
          awa: data.awa,
          raceId: data.raceId
        }
      });

      res.status(201).json({
        id: performance.id,
        timestamp: performance.timestamp,
        message: 'Performance data stored successfully'
      });
    } catch (error) {
      console.error('Error storing performance data:', error);
      res.status(500).json({ error: 'Failed to store performance data' });
    }
  });

  // Get performance statistics for a time range
  app.get(`${apiBase}/performance/stats`, async (req: Request, res: Response) => {
    try {
      const startTime = req.query.startTime ? new Date(req.query.startTime as string) : new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = req.query.endTime ? new Date(req.query.endTime as string) : new Date();
      const raceId = req.query.raceId as string;

      const whereClause: any = {
        timestamp: {
          gte: startTime,
          lte: endTime
        }
      };

      if (raceId) {
        whereClause.raceId = raceId;
      }

      const performances = await prisma.performanceData.findMany({
        where: whereClause,
        orderBy: { timestamp: 'asc' }
      });

      if (performances.length === 0) {
        return res.json({
          dataPoints: 0,
          timeRange: { startTime, endTime },
          stats: null
        });
      }

      // Calculate statistics
      const bspValues = performances.map(p => p.bsp);
      const vmgValues = performances.map(p => p.vmg);
      const percentPolarValues = performances.map(p => p.percentPolar);
      const twsValues = performances.map(p => p.tws);
      const twaValues = performances.map(p => p.twa);

      const stats = {
        boatSpeed: {
          avg: bspValues.reduce((a, b) => a + b, 0) / bspValues.length,
          max: Math.max(...bspValues),
          min: Math.min(...bspValues)
        },
        vmg: {
          avg: vmgValues.reduce((a, b) => a + b, 0) / vmgValues.length,
          max: Math.max(...vmgValues),
          min: Math.min(...vmgValues)
        },
        percentPolar: {
          avg: percentPolarValues.reduce((a, b) => a + b, 0) / percentPolarValues.length,
          max: Math.max(...percentPolarValues),
          min: Math.min(...percentPolarValues)
        },
        windSpeed: {
          avg: twsValues.reduce((a, b) => a + b, 0) / twsValues.length,
          max: Math.max(...twsValues),
          min: Math.min(...twsValues)
        },
        windAngle: {
          avg: twaValues.reduce((a, b) => a + b, 0) / twaValues.length,
          max: Math.max(...twaValues),
          min: Math.min(...twaValues)
        },
        timeOnTarget: {
          excellent: percentPolarValues.filter(p => p >= 100).length,
          good: percentPolarValues.filter(p => p >= 95 && p < 100).length,
          average: percentPolarValues.filter(p => p >= 90 && p < 95).length,
          poor: percentPolarValues.filter(p => p < 90).length
        }
      };

      // Round values
      Object.keys(stats).forEach(key => {
        if (key !== 'timeOnTarget' && typeof stats[key] === 'object') {
          Object.keys(stats[key]).forEach(subKey => {
            stats[key][subKey] = Math.round(stats[key][subKey] * 10) / 10;
          });
        }
      });

      res.json({
        dataPoints: performances.length,
        timeRange: { startTime, endTime },
        raceId,
        stats
      });
    } catch (error) {
      console.error('Error calculating performance stats:', error);
      res.status(500).json({ error: 'Failed to calculate performance statistics' });
    }
  });

  // Get tack/jibe analysis
  app.get(`${apiBase}/performance/maneuvers`, async (req: Request, res: Response) => {
    try {
      const startTime = req.query.startTime ? new Date(req.query.startTime as string) : new Date(Date.now() - 24 * 60 * 60 * 1000);
      const endTime = req.query.endTime ? new Date(req.query.endTime as string) : new Date();
      const raceId = req.query.raceId as string;

      const whereClause: any = {
        startTime: {
          gte: startTime,
          lte: endTime
        }
      };

      if (raceId) {
        whereClause.raceId = raceId;
      }

      const maneuvers = await prisma.tackJibeManeuver.findMany({
        where: whereClause,
        orderBy: { startTime: 'desc' }
      });

      const response = maneuvers.map(maneuver => ({
        id: maneuver.id,
        type: maneuver.type,
        startTime: maneuver.startTime,
        endTime: maneuver.endTime,
        durationSeconds: maneuver.durationSeconds,
        distanceLostMeters: maneuver.distanceLostMeters,
        entrySpeed: maneuver.entrySpeed,
        exitSpeed: maneuver.exitSpeed,
        avgSpeedDuringManeuver: maneuver.avgSpeedDuringManeuver,
        raceId: maneuver.raceId,
        efficiency: maneuver.exitSpeed / maneuver.entrySpeed // Calculate efficiency ratio
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching maneuver analysis:', error);
      res.status(500).json({ error: 'Failed to fetch maneuver analysis' });
    }
  });

  // Calculate optimal VMG angle for current conditions
  app.get(`${apiBase}/performance/optimal-angle`, async (req: Request, res: Response) => {
    try {
      const tws = parseFloat(req.query.tws as string);
      const isUpwind = req.query.isUpwind === 'true';

      if (isNaN(tws)) {
        return res.status(400).json({ error: 'Invalid tws parameter' });
      }

      const optimalAngle = performanceCalculator.calculateOptimalVMGAngle(tws, isUpwind);
      
      res.json({
        tws,
        isUpwind,
        optimalAngle,
        message: `Optimal ${isUpwind ? 'upwind' : 'downwind'} VMG angle for ${tws} knots TWS`
      });
    } catch (error) {
      console.error('Error calculating optimal angle:', error);
      res.status(500).json({ error: 'Failed to calculate optimal angle' });
    }
  });

  // Calculate laylines to a mark
  app.get(`${apiBase}/performance/laylines`, async (req: Request, res: Response) => {
    try {
      const currentLat = parseFloat(req.query.currentLat as string);
      const currentLon = parseFloat(req.query.currentLon as string);
      const markLat = parseFloat(req.query.markLat as string);
      const markLon = parseFloat(req.query.markLon as string);
      const tws = parseFloat(req.query.tws as string);

      if (isNaN(currentLat) || isNaN(currentLon) || isNaN(markLat) || isNaN(markLon) || isNaN(tws)) {
        return res.status(400).json({ error: 'Invalid coordinate or wind speed parameters' });
      }

      const laylines = performanceCalculator.calculateLaylines(currentLat, currentLon, markLat, markLon, tws);
      
      res.json({
        currentPosition: { lat: currentLat, lon: currentLon },
        markPosition: { lat: markLat, lon: markLon },
        tws,
        laylines: {
          port: laylines.port,
          starboard: laylines.starboard
        }
      });
    } catch (error) {
      console.error('Error calculating laylines:', error);
      res.status(500).json({ error: 'Failed to calculate laylines' });
    }
  });

  // Get smoothing window statistics
  app.get(`${apiBase}/performance/smoothing-stats`, async (req: Request, res: Response) => {
    try {
      const stats = performanceCalculator.getSmoothingStats();
      res.json(stats);
    } catch (error) {
      console.error('Error getting smoothing stats:', error);
      res.status(500).json({ error: 'Failed to get smoothing statistics' });
    }
  });

  // Clear smoothing window
  app.post(`${apiBase}/performance/clear-smoothing`, async (req: Request, res: Response) => {
    try {
      performanceCalculator.clearSmoothingWindow();
      res.json({ message: 'Smoothing window cleared successfully' });
    } catch (error) {
      console.error('Error clearing smoothing window:', error);
      res.status(500).json({ error: 'Failed to clear smoothing window' });
    }
  });

  // Delete old performance data
  app.delete(`${apiBase}/performance/cleanup`, async (req: Request, res: Response) => {
    try {
      const daysToKeep = parseInt(req.query.daysToKeep as string) || 30;
      const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

      const result = await prisma.performanceData.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate
          },
          raceId: null // Only delete non-race data
        }
      });

      res.json({
        message: 'Performance data cleanup completed',
        deletedRecords: result.count,
        cutoffDate,
        daysToKeep
      });
    } catch (error) {
      console.error('Error cleaning up performance data:', error);
      res.status(500).json({ error: 'Failed to cleanup performance data' });
    }
  });
}
