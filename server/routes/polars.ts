import { Express, Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { z } from 'zod';
import { PolarEngine } from '../services/polar-engine.js';

// Validation schemas
const PolarEntrySchema = z.object({
  tws: z.number().min(0).max(50),
  twa: z.number().min(0).max(180),
  targetSpeed: z.number().min(0).max(30),
  sailConfiguration: z.string().optional()
});

const PolarTableSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  entries: z.array(PolarEntrySchema).min(1)
});

export function setupPolarRoutes(
  app: Express,
  prisma: PrismaClient,
  polarEngine: PolarEngine,
  apiBase: string
): void {
  
  // Get all polar tables
  app.get(`${apiBase}/polars`, async (req: Request, res: Response) => {
    try {
      const polars = await prisma.polarTable.findMany({
        include: {
          entries: true,
          _count: {
            select: { entries: true }
          }
        },
        orderBy: { name: 'asc' }
      });

      const response = polars.map(polar => ({
        id: polar.id,
        name: polar.name,
        description: polar.description,
        isActive: polar.isActive,
        entryCount: polar._count.entries,
        createdAt: polar.createdAt,
        updatedAt: polar.updatedAt,
        entries: polar.entries.map(entry => ({
          tws: entry.tws,
          twa: entry.twa,
          targetSpeed: entry.targetSpeed,
          sailConfiguration: entry.sailConfiguration
        }))
      }));

      res.json(response);
    } catch (error) {
      console.error('Error fetching polars:', error);
      res.status(500).json({ error: 'Failed to fetch polar tables' });
    }
  });

  // Get specific polar table
  app.get(`${apiBase}/polars/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      
      const polar = await prisma.polarTable.findUnique({
        where: { id },
        include: { entries: true }
      });

      if (!polar) {
        return res.status(404).json({ error: 'Polar table not found' });
      }

      const response = {
        id: polar.id,
        name: polar.name,
        description: polar.description,
        isActive: polar.isActive,
        createdAt: polar.createdAt,
        updatedAt: polar.updatedAt,
        entries: polar.entries.map(entry => ({
          tws: entry.tws,
          twa: entry.twa,
          targetSpeed: entry.targetSpeed,
          sailConfiguration: entry.sailConfiguration
        }))
      };

      res.json(response);
    } catch (error) {
      console.error('Error fetching polar:', error);
      res.status(500).json({ error: 'Failed to fetch polar table' });
    }
  });

  // Create new polar table
  app.post(`${apiBase}/polars`, async (req: Request, res: Response) => {
    try {
      const validation = PolarTableSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid polar table data',
          details: validation.error.errors
        });
      }

      const { name, description, entries } = validation.data;

      // Check for duplicate name
      const existing = await prisma.polarTable.findUnique({
        where: { name }
      });

      if (existing) {
        return res.status(409).json({ error: 'Polar table with this name already exists' });
      }

      const polar = await prisma.polarTable.create({
        data: {
          name,
          description,
          isActive: false,
          entries: {
            create: entries.map(entry => ({
              tws: entry.tws,
              twa: entry.twa,
              targetSpeed: entry.targetSpeed,
              sailConfiguration: entry.sailConfiguration
            }))
          }
        },
        include: { entries: true }
      });

      res.status(201).json({
        id: polar.id,
        name: polar.name,
        description: polar.description,
        isActive: polar.isActive,
        createdAt: polar.createdAt,
        entries: polar.entries.map(entry => ({
          tws: entry.tws,
          twa: entry.twa,
          targetSpeed: entry.targetSpeed,
          sailConfiguration: entry.sailConfiguration
        }))
      });
    } catch (error) {
      console.error('Error creating polar:', error);
      res.status(500).json({ error: 'Failed to create polar table' });
    }
  });

  // Update polar table
  app.put(`${apiBase}/polars/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;
      const validation = PolarTableSchema.safeParse(req.body);
      
      if (!validation.success) {
        return res.status(400).json({ 
          error: 'Invalid polar table data',
          details: validation.error.errors
        });
      }

      const { name, description, entries } = validation.data;

      // Check if polar exists
      const existing = await prisma.polarTable.findUnique({
        where: { id }
      });

      if (!existing) {
        return res.status(404).json({ error: 'Polar table not found' });
      }

      // Check for duplicate name (excluding current polar)
      const nameConflict = await prisma.polarTable.findFirst({
        where: { 
          name,
          id: { not: id }
        }
      });

      if (nameConflict) {
        return res.status(409).json({ error: 'Polar table with this name already exists' });
      }

      // Update using transaction
      const polar = await prisma.$transaction(async (tx) => {
        // Delete existing entries
        await tx.polarEntry.deleteMany({
          where: { polarTableId: id }
        });

        // Update polar table with new entries
        return await tx.polarTable.update({
          where: { id },
          data: {
            name,
            description,
            entries: {
              create: entries.map(entry => ({
                tws: entry.tws,
                twa: entry.twa,
                targetSpeed: entry.targetSpeed,
                sailConfiguration: entry.sailConfiguration
              }))
            }
          },
          include: { entries: true }
        });
      });

      // Reload polar engine cache if this is the active polar
      if (polar.isActive) {
        await polarEngine.loadActivePolar();
      }

      res.json({
        id: polar.id,
        name: polar.name,
        description: polar.description,
        isActive: polar.isActive,
        updatedAt: polar.updatedAt,
        entries: polar.entries.map(entry => ({
          tws: entry.tws,
          twa: entry.twa,
          targetSpeed: entry.targetSpeed,
          sailConfiguration: entry.sailConfiguration
        }))
      });
    } catch (error) {
      console.error('Error updating polar:', error);
      res.status(500).json({ error: 'Failed to update polar table' });
    }
  });

  // Set active polar table
  app.post(`${apiBase}/polars/:id/activate`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if polar exists
      const polar = await prisma.polarTable.findUnique({
        where: { id }
      });

      if (!polar) {
        return res.status(404).json({ error: 'Polar table not found' });
      }

      // Set as active using polar engine
      await polarEngine.setActivePolar(id);

      res.json({ 
        message: 'Polar table activated successfully',
        activePolarId: id
      });
    } catch (error) {
      console.error('Error activating polar:', error);
      res.status(500).json({ error: 'Failed to activate polar table' });
    }
  });

  // Delete polar table
  app.delete(`${apiBase}/polars/:id`, async (req: Request, res: Response) => {
    try {
      const { id } = req.params;

      // Check if polar exists
      const polar = await prisma.polarTable.findUnique({
        where: { id }
      });

      if (!polar) {
        return res.status(404).json({ error: 'Polar table not found' });
      }

      // Prevent deletion of active polar
      if (polar.isActive) {
        return res.status(400).json({ error: 'Cannot delete active polar table' });
      }

      await prisma.polarTable.delete({
        where: { id }
      });

      res.json({ message: 'Polar table deleted successfully' });
    } catch (error) {
      console.error('Error deleting polar:', error);
      res.status(500).json({ error: 'Failed to delete polar table' });
    }
  });

  // Get target speed for specific conditions
  app.get(`${apiBase}/polars/target`, async (req: Request, res: Response) => {
    try {
      const tws = parseFloat(req.query.tws as string);
      const twa = parseFloat(req.query.twa as string);
      const seaState = req.query.seaState as string;

      if (isNaN(tws) || isNaN(twa)) {
        return res.status(400).json({ error: 'Invalid tws or twa parameters' });
      }

      const target = polarEngine.getTargetSpeed(tws, twa, seaState as any);
      
      res.json({
        tws,
        twa,
        seaState,
        targetSpeed: target.targetSpeed,
        targetVMG: target.targetVMG
      });
    } catch (error) {
      console.error('Error getting target speed:', error);
      res.status(500).json({ error: 'Failed to get target speed' });
    }
  });
}
