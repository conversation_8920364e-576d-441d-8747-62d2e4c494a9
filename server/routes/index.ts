import { Express } from 'express';
import { PrismaClient } from '@prisma/client';
import { PolarEngine } from '../services/polar-engine.js';
import { PerformanceCalculator } from '../services/performance-calculator.js';

import { setupPolarRoutes } from './polars.js';
import { setupRaceRoutes } from './races.js';
import { setupCrewRoutes } from './crew.js';
import { setupPerformanceRoutes } from './performance.js';
import { setupSailPlanRoutes } from './sailplan.js';
import { setupSettingsRoutes } from './settings.js';

export function setupRoutes(
  app: Express,
  prisma: PrismaClient,
  polarEngine: PolarEngine,
  performanceCalculator: PerformanceCalculator
): void {
  // API base path
  const apiBase = '/api';

  // Setup route modules
  setupPolarRoutes(app, prisma, polarEngine, apiBase);
  setupRaceRoutes(app, prisma, apiBase);
  setupCrewRoutes(app, prisma, apiBase);
  setupPerformanceRoutes(app, prisma, performanceCalculator, apiBase);
  setupSailPlanRoutes(app, prisma, apiBase);
  setupSettingsRoutes(app, prisma, apiBase);

  // API info endpoint
  app.get(`${apiBase}/info`, (req, res) => {
    res.json({
      name: 'Perspective Racing API',
      version: '1.0.0',
      description: 'Sailing performance and race management system',
      endpoints: {
        polars: `${apiBase}/polars`,
        races: `${apiBase}/races`,
        crew: `${apiBase}/crew`,
        performance: `${apiBase}/performance`,
        sailplan: `${apiBase}/sailplan`,
        settings: `${apiBase}/settings`
      }
    });
  });
}
