{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["ES2020"], "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "../dist/server", "rootDir": ".", "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowImportingTsExtensions": false, "paths": {"@/*": ["../*"]}}, "include": ["**/*", "../types.ts"], "exclude": ["node_modules", "../dist", "../node_modules"]}