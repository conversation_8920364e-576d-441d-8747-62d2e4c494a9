import React, { useContext, useState } from 'react';
import { LiveDataContext } from './contexts/LiveDataContext';
import DashboardScreen from './components/Dashboard/DashboardScreen';
import RacePlannerScreen from './components/RacePlanner/RacePlannerScreen';
import ReportScreen from './components/Report/ReportScreen';
import SettingsScreen from './components/Settings/SettingsScreen';
import CrewCalendarScreen from './components/CrewCalendar/CrewCalendarScreen';
import PercentPolarBadge from './components/Dashboard/PercentPolarBadge';
import { ROUTE_PATHS } from './constants';
import { NavLink, Routes, Route } from 'react-router-dom';
import { Button } from './components/ui/Button';
import { Icons } from './components/ui/icons';
import { ConnectionStatus } from './components/ui/status-indicator';

const Navigation: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const navLinkClass = ({ isActive }: { isActive: boolean }): string =>
    `px-3 py-2 md:px-4 rounded-md text-sm font-medium transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#131A2A] ${
      isActive
        ? 'bg-sky-500 text-white shadow-lg'
        : 'text-slate-300 hover:bg-sky-600 hover:text-white'
    }`;

  const mobileNavLinkClass = ({ isActive }: { isActive: boolean }): string =>
  `block w-full text-left px-4 py-3 rounded-md text-base font-medium transition-colors duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#131A2A] ${
    isActive
      ? 'bg-sky-500 text-white shadow-lg'
      : 'text-slate-300 hover:bg-sky-600 hover:text-white'
  }`;

  return (
    <nav className="bg-[#131A2A] shadow-xl sticky top-0 z-40 border-b border-[#334155]">
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <NavLink to={ROUTE_PATHS.DASHBOARD} className="flex-shrink-0 flex items-center focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 focus:ring-offset-[#131A2A] rounded-md">
              <div className="flex items-center space-x-2">
                <Icons.ship className="h-8 w-8 text-sky-400" />
                <span className="text-2xl font-extrabold">
                  <span className="text-slate-100">Perspective</span>
                  <span className="text-sky-400">Racing</span>
                </span>
              </div>
            </NavLink>
            <div className="hidden md:flex space-x-2 ml-8">
              <NavLink to={ROUTE_PATHS.DASHBOARD} className={navLinkClass}>Dashboard</NavLink>
              <NavLink to={ROUTE_PATHS.PLAN} className={navLinkClass}>Race Plan</NavLink>
              <NavLink to={ROUTE_PATHS.REPORT} className={navLinkClass}>Report</NavLink>
              <NavLink to={ROUTE_PATHS.SETTINGS} className={navLinkClass}>Settings</NavLink>
              <NavLink to={ROUTE_PATHS.CREW} className={navLinkClass}>Crew</NavLink>
            </div>
          </div>
          <div className="flex items-center">
            <div className="mr-3">
             <PercentPolarBadge />
            </div>
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                aria-label="Open main menu"
                className="text-slate-300 hover:text-white"
              >
                {isMobileMenuOpen ? (
                  <Icons.close className="h-6 w-6" />
                ) : (
                  <Icons.menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile menu */}
      {isMobileMenuOpen && (
        <div className="md:hidden border-t border-[#334155] bg-[#131A2A] shadow-lg">
          <div className="px-3 pt-2 pb-4 space-y-1 sm:px-4">
              <NavLink to={ROUTE_PATHS.DASHBOARD} className={mobileNavLinkClass} onClick={() => setIsMobileMenuOpen(false)}>Dashboard</NavLink>
              <NavLink to={ROUTE_PATHS.PLAN} className={mobileNavLinkClass} onClick={() => setIsMobileMenuOpen(false)}>Race Plan</NavLink>
              <NavLink to={ROUTE_PATHS.REPORT} className={mobileNavLinkClass} onClick={() => setIsMobileMenuOpen(false)}>Report</NavLink>
              <NavLink to={ROUTE_PATHS.SETTINGS} className={mobileNavLinkClass} onClick={() => setIsMobileMenuOpen(false)}>Settings</NavLink>
              <NavLink to={ROUTE_PATHS.CREW} className={mobileNavLinkClass} onClick={() => setIsMobileMenuOpen(false)}>Crew</NavLink>
          </div>
        </div>
      )}
    </nav>
  );
};

const AppLoadingState: React.FC = () => (
  <div className="min-h-screen flex flex-col items-center justify-center perspective-racing-app-bg text-slate-100 p-6 text-center">
    <div className="flex items-center space-x-4 mb-8">
      <Icons.ship className="h-16 w-16 text-sky-400" />
      <Icons.loading className="h-16 w-16 text-sky-500 animate-spin" />
    </div>
    <h1 className="text-3xl font-bold mb-3">
        <span className="text-slate-100">Perspective</span><span className="text-sky-400">Racing</span>
    </h1>
    <p className="text-xl text-slate-300 mb-2">Connecting to NMEA data stream</p>
    <p className="text-md text-slate-500">Ensure your NKE WiFi Box is active and correctly configured</p>
    <div className="mt-6">
      <ConnectionStatus isConnected={false} isConnecting={true} />
    </div>
  </div>
);


const App: React.FC = () => {
  const { liveData } = useContext(LiveDataContext);

  if (!liveData) {
    return <AppLoadingState />;
  }

  return (
    <div className="min-h-screen flex flex-col perspective-racing-app-bg">
      <Navigation />
      <main className="flex-grow container mx-auto p-4 sm:p-6 lg:p-8 xl:max-w-screen-xl w-full">
        <Routes>
          <Route path={ROUTE_PATHS.DASHBOARD} element={<DashboardScreen />} />
          <Route path={ROUTE_PATHS.PLAN} element={<RacePlannerScreen />} />
          <Route path={ROUTE_PATHS.REPORT} element={<ReportScreen />} />
          <Route path={ROUTE_PATHS.SETTINGS} element={<SettingsScreen />} />
          <Route path={ROUTE_PATHS.CREW} element={<CrewCalendarScreen />} />
        </Routes>
      </main>
      <footer className="bg-[#131A2A] text-center text-xs text-slate-400 p-5 border-t border-[#334155]">
        Perspective Racing &copy; {new Date().getFullYear()} - Advanced Sailing Analytics
      </footer>
    </div>
  );
};

export default App;