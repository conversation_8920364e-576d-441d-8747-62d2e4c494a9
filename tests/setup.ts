import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';

// Test database setup
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test.db'
    }
  }
});

beforeAll(async () => {
  // Setup test database
  await prisma.$connect();
});

afterAll(async () => {
  // Cleanup test database
  await prisma.$disconnect();
});

beforeEach(async () => {
  // Clean database before each test
  await prisma.performanceData.deleteMany();
  await prisma.tackJibeManeuver.deleteMany();
  await prisma.crewRSVP.deleteMany();
  await prisma.raceEvent.deleteMany();
  await prisma.crewMember.deleteMany();
  await prisma.sailPlan.deleteMany();
  await prisma.polarEntry.deleteMany();
  await prisma.polarTable.deleteMany();
  await prisma.settings.deleteMany();
});

afterEach(async () => {
  // Additional cleanup if needed
});

// Make prisma available globally for tests
global.prisma = prisma;
