# Database
DATABASE_URL="file:./dev.db"

# Server Configuration
PORT=8080
NODE_ENV=development
CLIENT_URL=http://localhost:5173

# NMEA Configuration
NMEA_HOST=localhost
NMEA_PORT=2000

# Security (generate secure values for production)
JWT_SECRET=your-jwt-secret-here
SESSION_SECRET=your-session-secret-here

# Optional: Firebase Configuration (for cloud sync)
FIREBASE_PROJECT_ID=
FIREBASE_PRIVATE_KEY=
FIREBASE_CLIENT_EMAIL=

# Optional: Weather API
WEATHER_API_KEY= 

# Optional: Email Configuration (for notifications)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=

# Logging
LOG_LEVEL=info
