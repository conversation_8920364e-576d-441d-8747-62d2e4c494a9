import net from 'net';

class NMEASimulator {
  constructor(port = 2000) {
    this.port = port;
    this.server = null;
    this.clients = new Set();
    this.isRunning = false;

    // Simulation state
    this.state = {
      lat: 34.0522,      // Los Angeles area
      lon: -118.2437,
      heading: 45,       // True heading
      bsp: 7.5,          // Boat speed
      tws: 12,           // True wind speed
      twa: 45,           // True wind angle
      heel: 8,           // Heel angle
      pitch: 2,          // Pitch angle
      roll: -1,          // Roll angle
      depth: 15          // Depth in meters
    };

    this.variations = {
      heading: { rate: 0.5, range: 10 },
      bsp: { rate: 0.3, range: 2 },
      tws: { rate: 0.2, range: 3 },
      twa: { rate: 0.4, range: 15 },
      heel: { rate: 0.6, range: 5 },
      pitch: { rate: 0.8, range: 3 },
      roll: { rate: 1.0, range: 4 }
    };
  }

  start() {
    this.server = net.createServer((socket) => {
      console.log(`Client connected: ${socket.remoteAddress}:${socket.remotePort}`);
      this.clients.add(socket);

      socket.on('close', () => {
        console.log(`Client disconnected: ${socket.remoteAddress}:${socket.remotePort}`);
        this.clients.delete(socket);
      });

      socket.on('error', (err) => {
        console.error('Socket error:', err);
        this.clients.delete(socket);
      });
    });

    this.server.listen(this.port, () => {
      console.log(`🌊 NMEA Simulator listening on port ${this.port}`);
      this.isRunning = true;
      this.startSimulation();
    });

    this.server.on('error', (err) => {
      console.error('Server error:', err);
    });
  }

  stop() {
    this.isRunning = false;
    if (this.server) {
      this.server.close();
    }
  }

  startSimulation() {
    const sendData = () => {
      if (!this.isRunning) return;

      this.updateState();
      const sentences = this.generateNMEASentences();

      sentences.forEach(sentence => {
        this.broadcast(sentence);
      });

      setTimeout(sendData, 2000); // Send data every 2 seconds
    };

    sendData();
  }

  updateState() {
    // Add realistic variations to the sailing data
    Object.keys(this.variations).forEach(key => {
      const variation = this.variations[key];
      const change = (Math.random() - 0.5) * variation.rate;
      this.state[key] += change;

      // Apply realistic bounds
      switch (key) {
        case 'heading':
          this.state[key] = (this.state[key] + 360) % 360;
          break;
        case 'bsp':
          this.state[key] = Math.max(0, Math.min(15, this.state[key]));
          break;
        case 'tws':
          this.state[key] = Math.max(0, Math.min(30, this.state[key]));
          break;
        case 'twa':
          this.state[key] = Math.max(20, Math.min(170, this.state[key]));
          break;
        case 'heel':
          this.state[key] = Math.max(-30, Math.min(30, this.state[key]));
          break;
        case 'pitch':
          this.state[key] = Math.max(-10, Math.min(10, this.state[key]));
          break;
        case 'roll':
          this.state[key] = Math.max(-15, Math.min(15, this.state[key]));
          break;
      }
    });

    // Simulate boat movement
    const speedKnots = this.state.bsp;
    const headingRad = this.state.heading * Math.PI / 180;

    // Convert speed to degrees per second (very rough approximation)
    const speedDegPerSec = speedKnots * 0.000005;

    this.state.lat += Math.cos(headingRad) * speedDegPerSec * 2; // 2 second interval
    this.state.lon += Math.sin(headingRad) * speedDegPerSec * 2;
  }

  generateNMEASentences() {
    const sentences = [];
    const now = new Date();
    const timeStr = now.toISOString().substr(11, 8).replace(/:/g, '');

    // VHW - Water speed and heading
    const vhw = `$IIVHW,${this.state.heading.toFixed(1)},T,,M,${this.state.bsp.toFixed(1)},N,,K`;
    sentences.push(this.addChecksum(vhw));

    // MWV - Wind speed and angle (True)
    const mwvTrue = `$IIMWV,${this.state.twa.toFixed(1)},T,${this.state.tws.toFixed(1)},N,A`;
    sentences.push(this.addChecksum(mwvTrue));

    // MWV - Wind speed and angle (Apparent)
    const awa = this.state.twa * 0.8; // Approximate apparent wind angle
    const aws = this.state.tws * 1.2; // Approximate apparent wind speed
    const mwvApp = `$IIMWV,${awa.toFixed(1)},R,${aws.toFixed(1)},N,A`;
    sentences.push(this.addChecksum(mwvApp));

    // GLL - Geographic position
    const latDeg = Math.floor(Math.abs(this.state.lat));
    const latMin = (Math.abs(this.state.lat) - latDeg) * 60;
    const latStr = `${latDeg.toString().padStart(2, '0')}${latMin.toFixed(4)}`;
    const latDir = this.state.lat >= 0 ? 'N' : 'S';

    const lonDeg = Math.floor(Math.abs(this.state.lon));
    const lonMin = (Math.abs(this.state.lon) - lonDeg) * 60;
    const lonStr = `${lonDeg.toString().padStart(3, '0')}${lonMin.toFixed(4)}`;
    const lonDir = this.state.lon >= 0 ? 'E' : 'W';

    const gll = `$IIGLL,${latStr},${latDir},${lonStr},${lonDir},${timeStr},A,A`;
    sentences.push(this.addChecksum(gll));

    // HDT - True heading
    const hdt = `$IIHDT,${this.state.heading.toFixed(1)},T`;
    sentences.push(this.addChecksum(hdt));

    // XDR - Heel angle
    const xdrHeel = `$IIXDR,A,${this.state.heel.toFixed(1)},D,HEEL`;
    sentences.push(this.addChecksum(xdrHeel));

    // XDR - Pitch angle
    const xdrPitch = `$IIXDR,A,${this.state.pitch.toFixed(1)},D,PITCH`;
    sentences.push(this.addChecksum(xdrPitch));

    // XDR - Roll angle
    const xdrRoll = `$IIXDR,A,${this.state.roll.toFixed(1)},D,ROLL`;
    sentences.push(this.addChecksum(xdrRoll));

    // DBT - Depth below transducer
    const dbt = `$IIDBT,${(this.state.depth * 3.28084).toFixed(1)},f,${this.state.depth.toFixed(1)},M,${(this.state.depth * 0.546807).toFixed(1)},F`;
    sentences.push(this.addChecksum(dbt));

    return sentences;
  }

  addChecksum(sentence) {
    let checksum = 0;
    for (let i = 1; i < sentence.length; i++) {
      checksum ^= sentence.charCodeAt(i);
    }
    return `${sentence}*${checksum.toString(16).toUpperCase().padStart(2, '0')}\r\n`;
  }

  broadcast(data) {
    this.clients.forEach(client => {
      try {
        client.write(data);
      } catch (err) {
        console.error('Error sending data to client:', err);
        this.clients.delete(client);
      }
    });
  }
}

// Start the simulator
const port = process.env.PORT || 2000;
const simulator = new NMEASimulator(port);

process.on('SIGINT', () => {
  console.log('\n🛑 Stopping NMEA simulator...');
  simulator.stop();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Stopping NMEA simulator...');
  simulator.stop();
  process.exit(0);
});

simulator.start();
