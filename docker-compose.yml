version: '3.8'

services:
  perspective-racing:
    build: .
    ports:
      - "3000:3000"  # Frontend
      - "8080:8080"  # Backend API
    environment:
      - NODE_ENV=production
      - DATABASE_URL=file:/app/data/production.db
      - NMEA_HOST=${NMEA_HOST:-localhost}
      - NMEA_PORT=${NMEA_PORT:-2000}
      - CLIENT_URL=http://localhost:3000
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: NMEA simulator for testing
  nmea-simulator:
    image: node:18-alpine
    working_dir: /app
    volumes:
      - ./tools/nmea-simulator:/app
    command: ["node", "simulator.js"]
    ports:
      - "2000:2000"
    environment:
      - PORT=2000
    profiles:
      - testing

  # Optional: Database backup service
  db-backup:
    image: alpine:latest
    volumes:
      - ./data:/data
      - ./backups:/backups
    command: >
      sh -c "
        while true; do
          cp /data/production.db /backups/backup-$(date +%Y%m%d-%H%M%S).db
          find /backups -name 'backup-*.db' -mtime +7 -delete
          sleep 86400
        done
      "
    profiles:
      - backup

volumes:
  data:
  logs:
  backups:
