{"name": "perspective-racing", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "vite", "dev:server": "tsx watch server/index.ts", "build": "npm run build:client && npm run build:server", "build:client": "vite build", "build:server": "tsc -p server/tsconfig.json", "preview": "vite preview", "start": "node dist/server/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "test": "jest", "test:watch": "jest --watch", "docker:build": "docker build -t perspective-racing .", "docker:run": "docker run -p 3000:3000 -p 8080:8080 perspective-racing"}, "dependencies": {"@prisma/client": "^5.7.1", "clsx": "^2.1.0", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "leaflet": "^1.9.4", "lucide-react": "^0.263.1", "morgan": "^1.10.0", "prisma": "^5.7.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "recharts": "^2.15.3", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^2.2.0", "zod": "^3.22.4"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/leaflet": "^1.9.8", "@types/morgan": "^1.9.9", "@types/node": "^22.14.0", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "jest": "^29.7.0", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "~5.7.2", "vite": "^6.2.0"}}